<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
      <TargetFramework>net9.0</TargetFramework>
    <Configurations>Debug;Release;Staging</Configurations>
    <WarningsAsErrors>CS4014</WarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="33.0.1"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.0"/>
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="9.0.3"/>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7"/>
  </ItemGroup>

</Project>
