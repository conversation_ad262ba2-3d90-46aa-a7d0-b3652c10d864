using AutoMapper;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Shared.UIBuilder.DTO;
using FlexCharge.Common.Telemetry;
using FlexCharge.WorkflowEngine.Activities;
using FlexCharge.WorkflowEngine.DTO;
using FlexCharge.WorkflowEngine.Entities;
using FlexCharge.WorkflowEngine.Services.WorkflowsService;
using Newtonsoft.Json;
using WorkflowDefinition = FlexCharge.WorkflowEngine.Workflows.WorkflowDefinition;

namespace FlexCharge.WorkflowEngine.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [JwtAuth]
    public partial class WorkflowsController : BaseController
    {
        private readonly AppOptions _globalData;
        private readonly IWorkflowsService _workflowsService;
        private readonly IMapper _mapper;
        private readonly IActivityService _activityService;

        public WorkflowsController(IWorkflowsService workflowsService,
            IMapper mapper,
            IOptions<AppOptions> globalData,
            IActivityService activityService
        )
        {
            _workflowsService = workflowsService;
            _mapper = mapper;
            _globalData = globalData.Value;
            _activityService = activityService;
        }

        [HttpGet()] // GET ALL
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(WorkflowReponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Get(string? query,
            [FromQuery] List<WorkflowStatuses>? status,
            [FromQuery] List<string>? domains,
            DateTime? from,
            DateTime? to,
            DateTime? modifiedFrom,
            DateTime? modifiedTo,
            DateTime? runFrom,
            DateTime? runTo,
            CancellationToken token,
            int pageSize = 10,
            int pageNumber = 1)
        {
            using var workspan = Workspan.StartEndpoint<WorkflowsController>(this, null, _globalData);

            try
            {
                if (!ModelState.IsValid) return BadRequest(ModelState);

                var response = await _workflowsService.GetWorkflows(query, status,
                    domains, from, to, modifiedFrom, modifiedTo, runFrom, runTo, pageSize, pageNumber);

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed getting workflows");
            }
        }

        [HttpGet("{id:guid}")] // GET BY ID
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(WorkflowReponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetWorkflow([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<WorkflowsController>(this, null, _globalData);

            try
            {
                //to open: http://localhost:3015/builder/3f3dcb71-fc05-4a78-951d-22d7e80f12bc

                WorkflowReponseDTO response = new();

                // response.Name = "Test Workflow";
                response.Id = id;

                // TODO: If no workflow definition exists, still return workflow if it exists
                var workflowDefinition = await _workflowsService.GetWorkflowDefinitionOrDefaultAsync(id);

                if (workflowDefinition == null)
                {
                    return NotFound();
                }

                var workflow = await _workflowsService.GetWorkflowByIdAsync(id);

                if (workflow == null)
                {
                    return NotFound();
                }

                response.Name = workflow.Name;
                response.WorkflowDefinition = workflowDefinition.SerializeDesign();

                return Ok(response);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed getting workflow");
            }
        }


        [HttpPost()] // CREATE
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(WorkflowReponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Post(WorkflowCreateDTO request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<WorkflowsController>(this, request, _globalData);

            try
            {
                if (!ModelState.IsValid) return BadRequest(ModelState);

                await _workflowsService.CreateWorkflowAsync(request);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed create workflow");
            }
        }

        [HttpPut()] // UPDATE
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(WorkflowReponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Put(WorkflowUpdateDTO request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<WorkflowsController>(this, request, _globalData);

            try
            {
                if (!ModelState.IsValid) return BadRequest(ModelState);

                await _workflowsService.UpdateWorkflowAsync(request);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed update workflow");
            }
        }

        [HttpDelete("{id:guid}")] // Delete
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(WorkflowReponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteWorkflow([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<WorkflowsController>(this, null, _globalData);

            try
            {
                await _workflowsService.DeleteWorkflowAsync(id);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed delete workflow");
            }
        }

        [HttpPost("{id:guid}/publish")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(WorkflowReponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PublishWorkflow([FromRoute] Guid id, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<WorkflowsController>(this, null, _globalData);

            try
            {
                await _workflowsService.PublishWorkflowAsync(id);

                return Ok();
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed publish workflow");
            }
        }

        [HttpPost("save")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(WorkflowReponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SaveWorkflow(WorkflowSaveDTO request, CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<WorkflowsController>(this, request, _globalData);

            try
            {
                var result = await _workflowsService.SaveWorkflowAsync(request);

                return Ok(result);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed save workflow");
            }
        }

        [HttpGet("{workflowId:guid}/get-actual-workflow-version-id")]
        [Authorize(MyPolicies.SUPER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetLatestWorkflowVersionId([FromRoute] Guid workflowId,
            CancellationToken token)
        {
            using var workspan = Workspan.StartEndpoint<WorkflowsController>(this, null, _globalData);

            try
            {
                var workflowDefinition = await _workflowsService.GetActualWorkflowIdOrDefaultAsync(workflowId);

                if (workflowDefinition == null)
                    return NotFound();

                return Ok(workflowDefinition.Id);
            }
            catch (Exception e)
            {
                workspan.RecordEndpointCriticalApiError(e);

                await _activityService.CreateActivityAsync(ApiErrorActivities.EndpointCriticalApiError, e,
                    set => set
                        .CorrelationId(GetMID()));

                return StatusCode(StatusCodes.Status500InternalServerError, "Failed getting workflow");
            }
        }
    }
}