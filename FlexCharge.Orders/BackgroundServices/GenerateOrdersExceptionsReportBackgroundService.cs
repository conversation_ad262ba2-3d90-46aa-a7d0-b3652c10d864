using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Reporting;
using FlexCharge.Common.Shared.Sftp;
using FlexCharge.Common.Telemetry;
using FlexCharge.Orders.Services.BatchServices.Batches;
using FlexCharge.Orders.Services.Reporting;
using FlexCharge.Orders.Services.Reporting.OrderExceptionsReport;
using FlexCharge.Orders.Services.Reporting.OrdersSnapshotReport;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Orders.Services.PayoutServices;

public class GenerateOrdersExceptionsReportBackgroundService : BackgroundWorkerCommand
{
    public GenerateOrdersExceptionsReportBackgroundService()
    {
    }

    protected override async Task ExecuteAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<GenerateOrdersExceptionsReportBackgroundService>()
            .LogEnterAndExit();

        try
        {
            var dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>();

            var exceptionsReportId = new Guid("326985b0-1848-4e7c-b787-f4733f246f33");
            var reportName = "order-exceptions";

            var listOfMerchants = await dbContext.MerchantReports
                .Include(x => x.Merchant)
                .Where(x => x.ReportTypeId == exceptionsReportId)
                .Select(x => new
                {
                    x.Merchant.Mid
                })
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            if (listOfMerchants == null || !listOfMerchants.Any())
            {
                workspan.Log.Information("No merchants found for Order Exceptions Report");
                return;
            }

            foreach (var merchant in listOfMerchants)
            {
                workspan.Log.Information("Processing merchant {MerchantId}", merchant.Mid);

                var reportingService = serviceProvider.GetRequiredService<IReportingService>();

                var result = await reportingService.GenerateAndStoreReportAsync(
                    exceptionsReportId,
                    DateTime.UtcNow.Date.AddDays(-1),
                    DateTime.UtcNow.Date.AddTicks(-1),
                    $"{merchant.Mid}/{SftpFolderNamesHelper.Reports}",
                    $"{merchant.Mid}_{reportName}_{DateTime.UtcNow:yyyyMMdd-HHmmss}",
                    new OrderExceptionsReportParams()
                    {
                        Mid = merchant.Mid
                    });


                workspan.Log.Information(
                    "Order Exceptions Report generated for merchant {MerchantId}",
                    merchant.Mid);
            }

            workspan.Log.Information("Finished GenerateOrdersSnapshotReportBackgroundService");
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: CalculateOrdersSnapshotReportCommand > ExecuteAsync");
        }
    }
}