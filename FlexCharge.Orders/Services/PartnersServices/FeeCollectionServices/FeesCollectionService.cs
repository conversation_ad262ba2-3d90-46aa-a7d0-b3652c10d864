using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.DistributedLock;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands;
using FlexCharge.Orders.DistributedLock;
using FlexCharge.Orders.Entities;
using FlexCharge.Orders.Services.PayoutServices;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace FlexCharge.Orders.Services.PartnersServices.FeeCollectionServices;

public class FeesCollectionService : IFeesCollectionService
{
    private readonly IBatchService _batchService;
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IRequestClient<GetPartnerFeesConfigCommand> _partnerFeesCommand;
    private readonly IRequestClient<GetPartnerTransactionStatusCommand> _partnerTransactionCommand;
    private readonly IDistributedLockService _distributedLockService;
    private readonly IPartnerService _partnerService;

    public FeesCollectionService(
        IBatchService batchService,
        PostgreSQLDbContext dbContext,
        IRequestClient<GetPartnerFeesConfigCommand> partnerFeesCommand,
        IRequestClient<GetPartnerTransactionStatusCommand> partnerTransactionCommand,
        IDistributedLockService distributedLockService,
        IPartnerService partnerService)
    {
        _batchService = batchService;
        _dbContext = dbContext;
        _partnerFeesCommand = partnerFeesCommand;
        _distributedLockService = distributedLockService;
        _partnerService = partnerService;
        _partnerTransactionCommand = partnerTransactionCommand;
    }


    public async Task ReCalculateFeesAsync(Guid pid, Guid batchId, CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<FeesCollectionService>()
            .Baggage("ServiceName", nameof(FeesCollectionService))
            .Baggage("MethodName", nameof(CalculateFeesAsync))
            .LogEnterAndExit();

        await using var @lock = await _distributedLockService.AcquireLockAsync(
            LockKeyFactory.ReCalculateFeesAsync(),
            TimeSpan.FromMinutes(5),
            TimeSpan.FromSeconds(10));

        var existingBatch = await _batchService.GetBatchByIdAsync(batchId);

        if (existingBatch == null)
            throw new FlexChargeException("ReCalculateFeesAsync ERROR: Can't find batch");

        if (existingBatch.BatchType != nameof(FIMovementType.FIPAD))
            throw new FlexChargeException("ReCalculateFeesAsync ERROR: Batch type is not FIPAD");

        var partnerAccounts = await _dbContext.FinancialAccounts
            .Where(x => x.RelatedEntityId == pid)
            .ToListAsync(cancellationToken);

        foreach (var financialAccount in partnerAccounts)
        {
            workspan
                .Baggage("PartnerId", financialAccount.RelatedEntityId)
                .Baggage("FinancialAccountId", financialAccount.Id);

            await using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);

            var partnerFeesConfig = (await _partnerFeesCommand.GetResponse<GetPartnerFeesConfigCommandResponse>(
                new GetPartnerFeesConfigCommand() {PartnerId = financialAccount.RelatedEntityId},
                cancellationToken)).Message;

            var partnerSettings =
                await _partnerService.GetPartnerSettingsAsync(financialAccount.RelatedEntityId, new HashSet<string>());

            workspan.Log.Information("partnerFeesConfig: {Fees}", JsonConvert.SerializeObject(partnerFeesConfig));

            if (!partnerFeesConfig.Fees.Any())
            {
                workspan.Log.Information("No partner fees found for partner");
                continue;
            }

            //setup culture for currency formatting
            var cultureInfo = new System.Globalization.CultureInfo("en-US");

            //get merchants related to this partner
            var merchants = await _dbContext.Merchants
                .Where(x => x.Pid == financialAccount.RelatedEntityId)
                .ToListAsync(cancellationToken);

            // get related orders
            var orders = await _dbContext.Orders
                .Where(x => merchants.Select(m => m.Mid)
                                .Contains(x.MerchantId) && x.FlexFeesBatchId == batchId &&
                            x.StatusCategory == OrderStatusCategory.completed.ToString())
                .Select(x => new
                {
                    Id = x.Id,
                    Amount = x.Amount,
                    MerchantId = x.MerchantId,
                    ReferenceNumber = x.ReferenceNumber
                })
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var orderCount = orders.Count;
            workspan.Log.Information(
                "Found {OrderCount} orders for partner", orderCount);

            //get relevant partner fees   
            var newBatch = await _batchService.GetOrCreateBatchV2(
                sender: existingBatch.Sender,
                receiver: existingBatch.Receiver,
                batchType: nameof(FIMovementType.FIPAD),
                batchStartDate: existingBatch.From,
                batchEndDate: existingBatch.To,
                isOffline: false,
                includeRecords: true,
                overrideExistingBatch: true,
                autoPosted: partnerSettings.PartnerFeesAutoCollectEnabled);

            var ordersProcessed = 0;
            foreach (var order in orders)
            {
                var selectedFees =
                    merchants?.SingleOrDefault(x => x.Mid == order.MerchantId)?.IntegrationPartnerId != null
                        ? partnerFeesConfig.Fees.Where(x => x.Group == "IntegrationPartnerFees").ToList()
                        : partnerFeesConfig.Fees.Where(x => x.Group == "StandAlone").ToList();


                if (!selectedFees.Any())
                {
                    workspan.Log.Information(
                        "Order loop: No partner fees found for partner {PartnerId} financialAccount id: {FinancialAccountId}",
                        financialAccount.RelatedEntityId, financialAccount.Id);

                    continue;
                }

                foreach (var partnerFee in selectedFees)
                {
                    workspan.Log.Information("Applying partner fee: {Fee}", partnerFee.Name);

                    var partnerFeeAmount = CalculateFee(partnerFee, order.Amount);
                    workspan.Log.Information("Calculated partner fee: {FeeAmount}", partnerFeeAmount);

                    var formattedOrderAmount = Formatters.IntToDecimal(order.Amount).ToString("C", cultureInfo);
                    var formattedAmount = Formatters.IntToDecimal(partnerFeeAmount).ToString("C", cultureInfo);

                    // add fee to partnerFeeBatch
                    var partnerFeeBatchItem = BatchRecord.Create(newBatch.Id,
                        batchRecordTypes: BatchRecordTypes.FlexPartnerFee,
                        direction: DirectionEnum.Debit,
                        amount: partnerFeeAmount,
                        description:
                        $"Partner fee for order {order.ReferenceNumber} with amount {formattedOrderAmount} and fee {formattedAmount}",
                        relatedOrderId: order.Id);

                    newBatch.TotalAmount += partnerFeeAmount;
                    newBatch.TotalCount += 1;

                    workspan.Log.Information("Adding partner fee record: {Record}",
                        JsonConvert.SerializeObject(partnerFeeBatchItem));
                    //add to batch records
                    newBatch.BatchRecords.Add(partnerFeeBatchItem);
                }

                //order.FlexFeesBatchId = partnerFeeBatch.Id;
                await _dbContext.Orders.Where(x => x.Id == order.Id)
                    .ExecuteUpdateAsync(settings => settings
                        .SetProperty(a => a.FlexFeesBatchId, a => newBatch.Id), cancellationToken);

                ordersProcessed++;
                workspan.Log.Information("Added order {OrdersProcessed}/{OrderCount} to partner fee batch: {OrderId}",
                    ordersProcessed, orderCount, order.Id);
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            //close transaction
            await transaction.CommitAsync(cancellationToken);

            workspan.Log.Information("Completed DAILY partner fees batch generation: {BatchId}", newBatch.Id);
        }
    }

    public async Task SyncFIPADBatchesStateAsync(CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<FeesCollectionService>()
            .Baggage("ServiceName", nameof(FeesCollectionService))
            .Baggage("MethodName", nameof(SyncFIPADBatchesStateAsync))
            .LogEnterAndExit();

        await using var @lock = await _distributedLockService.AcquireLockAsync(
            LockKeyFactory.SyncFeeBatchesStateAsync(),
            TimeSpan.FromMinutes(5),
            TimeSpan.FromSeconds(10));

        var batches = await _dbContext.Batches
            .Where(x => x.BatchType == nameof(FIMovementType.FIPAD) &&
                        x.PayoutStatus == nameof(BatchStatus.PROCESSING))
            .ToListAsync(cancellationToken);

        foreach (var batch in batches)
        {
            try
            {
                if (batch.RelatedTransaction == null)
                {
                    workspan.Log.Fatal("Batch {BatchId} has no related transaction", batch.Id);
                    continue;
                }

                workspan.Log.Information("Processing batch: {Batch}", JsonConvert.SerializeObject(batch));

                var transaction = await _partnerTransactionCommand
                    .GetResponse<GetPartnerTransactionCommandStatusResponse>(
                        new GetPartnerTransactionStatusCommand
                        {
                            TransactionId = batch.RelatedTransaction.Value
                        }, cancellationToken);

                if (!string.IsNullOrEmpty(transaction.Message.Error))
                {
                    workspan.Log.Error("Error getting transaction: {Error}", transaction.Message.Error);
                    continue;
                }

                workspan.Log.Information("Transaction: {Transaction}",
                    JsonConvert.SerializeObject(transaction.Message));

                var transactionStatus = transaction.Message.Status.Trim().ToLower();

                switch (transactionStatus)
                {
                    case "completed":
                        batch.PayoutStatus = nameof(BatchStatus.SUCCESS);
                        _dbContext.Batches.Update(batch);
                        await _dbContext.SaveChangesAsync(cancellationToken);
                        break;
                    case "failed" or "canceled":
                        batch.PayoutStatus = nameof(BatchStatus.FAILED);
                        batch.Comment = transaction.Message.ResponseMessage;
                        _dbContext.Batches.Update(batch);
                        await _dbContext.SaveChangesAsync(cancellationToken);
                        break;
                    case "inprocess":
                        workspan.Log.Information("Transaction is still in process");
                        break;
                    default:
                        workspan.Log.Error("Transaction status is: {Status}", transactionStatus);
                        break;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }


    public async Task CalculateFeesAsync(CancellationToken cancellationToken)
    {
        using var workspan = Workspan.Start<FeesCollectionService>()
            .Baggage("ServiceName", nameof(FeesCollectionService))
            .Baggage("MethodName", nameof(CalculateFeesAsync))
            .LogEnterAndExit();

        await using var @lock = await _distributedLockService.AcquireLockAsync(
            LockKeyFactory.CalculateFeesAsync(),
            TimeSpan.FromMinutes(5),
            TimeSpan.FromSeconds(10));

        #region Get MasterAccount for fees collection

        var flexFeesCollectionAccount = await _dbContext.FinancialAccounts
            .FirstOrDefaultAsync(x =>
                    x.RelatedEntityType == FinancialAccountsRelatedEntityType.MasterAccount &&
                    x.AccountType == FinancialAccountsTypes.BankAccountAch,
                cancellationToken);

        if (flexFeesCollectionAccount == null)
            throw new FlexChargeException("CalculateFeesAsync ERROR: Can't find flex financial account");

        #endregion

        //get partners
        var partnerFinancialAccounts = await _dbContext.FinancialAccounts
            .Where(x => x.RelatedEntityType == FinancialAccountsRelatedEntityType.Partner)
            .ToListAsync(cancellationToken);

        foreach (var financialAccount in partnerFinancialAccounts)
        {
            workspan
                .Baggage("PartnerId", financialAccount.RelatedEntityId)
                .Baggage("FinancialAccountId", financialAccount.Id);

            await using var transaction = await _dbContext.Database.BeginTransactionAsync(cancellationToken);

            var partnerFeesConfig = (await _partnerFeesCommand.GetResponse<GetPartnerFeesConfigCommandResponse>(
                new GetPartnerFeesConfigCommand() {PartnerId = financialAccount.RelatedEntityId},
                cancellationToken)).Message;

            workspan.Log.Information("partnerFeesConfig: {Fees}", JsonConvert.SerializeObject(partnerFeesConfig));

            if (!partnerFeesConfig.Fees.Any())
            {
                workspan.Log.Warning(
                    "No partner fees found for partner");
                continue;
            }

            var partnerSettings =
                await _partnerService.GetPartnerSettingsAsync(financialAccount.RelatedEntityId,
                    new HashSet<string>() {nameof(PartnerSettings.PartnerFeesAutoCollectEnabled)});

            if (partnerSettings == null)
            {
                workspan.Log.Warning(
                    "No partner settings found for partner");
                continue;
            }

            workspan.Log.Information("partnerSettings: {Settings}", JsonConvert.SerializeObject(partnerSettings));

            //setup culture for currency formatting
            var cultureInfo = new System.Globalization.CultureInfo("en-US");

            //get merchants with partnerId
            var merchants = await _dbContext.Merchants
                .Where(x => x.Pid == financialAccount.RelatedEntityId)
                .ToListAsync(cancellationToken);

            //get approved orders without feesbatch id for the merchants
            var orders = await _dbContext.Orders
                .Where(x => merchants.Select(m => m.Mid)
                                .Contains(x.MerchantId) && x.FlexFeesBatchId == null &&
                            x.StatusCategory == OrderStatusCategory.completed.ToString())
                .Select(x => new
                {
                    Id = x.Id,
                    Amount = x.Amount,
                    MerchantId = x.MerchantId,
                    ReferenceNumber = x.ReferenceNumber
                })
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var orderCount = orders.Count;
            workspan.Log.Information(
                "Found {OrderCount} orders for partner {PartnerId} financialAccount id: {FinancialAccountId}",
                orderCount, financialAccount.RelatedEntityId, financialAccount.Id);

            //get relevant partner fees   
            var partnerFeeBatch = await _batchService.GetOrCreateBatchV2(
                sender: financialAccount,
                receiver: flexFeesCollectionAccount,
                batchType: nameof(FIMovementType.FIPAD),
                batchStartDate: DateTime.Today.Date.ToUtcDateTime(),
                batchEndDate: DateTime.Today.AddDays(1).AddTicks(-1).ToUtcDateTime(),
                isOffline: false,
                includeRecords: true,
                overrideExistingBatch: true,
                autoPosted: partnerSettings.PartnerFeesAutoCollectEnabled);

            var ordersProcessed = 0;
            foreach (var order in orders)
            {
                var selectedFees =
                    merchants?.SingleOrDefault(x => x.Mid == order.MerchantId)?.IntegrationPartnerId != null
                        ? partnerFeesConfig.Fees.Where(x => x.Group == "IntegrationPartnerFees").ToList()
                        : partnerFeesConfig.Fees.Where(x => x.Group == "StandAlone").ToList();


                if (!selectedFees.Any())
                {
                    workspan.Log.Information(
                        "Order loop: No partner fees found for partner {PartnerId} financialAccount id: {FinancialAccountId}",
                        financialAccount.RelatedEntityId, financialAccount.Id);

                    continue;
                }

                foreach (var partnerFee in selectedFees)
                {
                    workspan.Log.Information("Applying partner fee: {Fee}", partnerFee.Name);

                    var partnerFeeAmount = CalculateFee(partnerFee, order.Amount);
                    workspan.Log.Information("Calculated partner fee: {FeeAmount}", partnerFeeAmount);

                    var formattedOrderAmount = Formatters.IntToDecimal(order.Amount).ToString("C", cultureInfo);
                    var formattedAmount = Formatters.IntToDecimal(partnerFeeAmount).ToString("C", cultureInfo);

                    // add fee to partnerFeeBatch
                    var partnerFeeBatchItem = BatchRecord.Create(partnerFeeBatch.Id,
                        batchRecordTypes: BatchRecordTypes.FlexPartnerFee,
                        direction: DirectionEnum.Debit,
                        amount: partnerFeeAmount,
                        description:
                        $"Partner fee for order {order.ReferenceNumber} with amount {formattedOrderAmount} and fee {formattedAmount}",
                        relatedOrderId: order.Id);

                    partnerFeeBatch.TotalAmount += partnerFeeAmount;
                    partnerFeeBatch.TotalCount += 1;

                    workspan.Log.Information("Adding partner fee record: {Record}",
                        JsonConvert.SerializeObject(partnerFeeBatchItem));
                    //add to batch records
                    partnerFeeBatch.BatchRecords.Add(partnerFeeBatchItem);
                }

                //order.FlexFeesBatchId = partnerFeeBatch.Id;
                await _dbContext.Orders.Where(x => x.Id == order.Id)
                    .ExecuteUpdateAsync(settings => settings
                        .SetProperty(a => a.FlexFeesBatchId, a => partnerFeeBatch.Id), cancellationToken);

                ordersProcessed++;
                workspan.Log.Information("Added order {OrdersProcessed}/{OrderCount} to partner fee batch: {OrderId}",
                    ordersProcessed, orderCount, order.Id);
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            //close transaction
            await transaction.CommitAsync(cancellationToken);

            workspan.Log.Information("Completed DAILY partner fees batch generation: {BatchId}", partnerFeeBatch.Id);
        }
    }

    public static int CalculateFee(GetPartnerFeesConfigCommandResponse.PartnerFeeModel partnerFee, int amount)
    {
        // If the fee amount is zero, the fee is zero regardless of fee type
        if (partnerFee.Amount == 0)
            return 0;

        // Calculate the fee using decimal to avoid unwanted integer math issues
        decimal partnerFeeAmountDecimal = partnerFee.Type.ToLower() switch
        {
            // "fixed" means the fee is exactly partnerFee.Amount (already in integer form)
            "fixed" => partnerFee.Amount,

            // "percentage" means partnerFee.Amount is in basis points
            // e.g., 100 basis points = 1.00% ; 250 = 2.50%, etc.
            // We do: (amount * partnerFee.Amount) / 10000
            "percentage" => (amount * (decimal) partnerFee.Amount) / 10000m,

            // If the fee type is unrecognized, default to 0
            _ => 0m
        };

        // If MinimumFeeAmount is specified and the calculated fee is below that minimum, use the minimum
        if (partnerFee.MinimumFeeAmount.HasValue && partnerFeeAmountDecimal < partnerFee.MinimumFeeAmount.Value)
        {
            partnerFeeAmountDecimal = partnerFee.MinimumFeeAmount.Value;
        }

        // Casting decimal->int truncates any fractional part (no rounding)
        return (int) partnerFeeAmountDecimal;
    }
}