# Public API Controllers

This folder contains controllers that expose public APIs for external consumption by partners and other authorized users.

## Structure

### PartnerApiKeysController
**Route**: `/api/PartnerApiKeys`

Provides secure API key management functionality for Partner Admins.

#### Endpoints

##### GET `/api/PartnerApiKeys`
- **Description**: Get API keys for the authenticated partner
- **Authorization**: PARTNER_ADMINS_ONLY
- **Parameters**:
  - `pageSize` (query, optional): Number of items per page (default: 10, max: 100)
  - `pageNumber` (query, optional): Page number (default: 1)
  - `type` (query, optional): Filter by key type (e.g., "production", "sandbox")
  - `revoked` (query, optional): Filter by revoked status
- **Response**: `PartnerApiKeysResponseDTO` with paginated results
- **Security**: Value field is always excluded from responses

##### POST `/api/PartnerApiKeys`
- **Description**: Create a new API key for the authenticated partner
- **Authorization**: PARTNER_ADMINS_ONLY
- **Body**: `PartnerApiKeyCreateDTO`
- **Response**: `ApiKeyGenerationResult` (includes the secret value - shown only once)
- **Security**: Validates partner ownership of client ID if provided

##### PUT `/api/PartnerApiKeys/{keyId}`
- **Description**: Update an existing API key (description, note, type, scopes)
- **Authorization**: PARTNER_ADMINS_ONLY
- **Parameters**: `keyId` (route): API key ID
- **Body**: `PartnerApiKeyUpdateDTO`
- **Response**: Success response
- **Security**: Validates partner ownership of the API key

##### DELETE `/api/PartnerApiKeys/{keyId}`
- **Description**: Revoke an API key
- **Authorization**: PARTNER_ADMINS_ONLY
- **Parameters**: `keyId` (route): API key ID to revoke
- **Response**: Success response
- **Security**: Validates partner ownership of the API key

## Security Features

1. **Partner Context Validation**: All operations validate partner ID from JWT claims
2. **Value Field Exclusion**: API key values are never returned in query responses
3. **Cross-Partner Protection**: Prevents access to other partners' resources
4. **Authorization Enforcement**: All endpoints require PARTNER_ADMIN role
5. **Audit Logging**: All operations are logged with partner ID for audit trail

## Design Principles

1. **No AutoMapper**: Uses manual mapping for better performance and control
2. **Explicit Security**: Security measures are explicit and visible
3. **RESTful Design**: Follows REST conventions for resource management
4. **Comprehensive Validation**: Input validation at multiple levels
5. **Error Handling**: Consistent error responses and proper HTTP status codes

## Usage Examples

### Get API Keys
```http
GET /api/PartnerApiKeys?pageSize=20&type=production
Authorization: Bearer {jwt_token}
```

### Create API Key
```http
POST /api/PartnerApiKeys
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "description": "Production API Key",
  "expiration": 525600,
  "type": "production",
  "scopes": ["read:orders", "write:orders"]
}
```

### Update API Key
```http
PUT /api/PartnerApiKeys/{keyId}
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "description": "Updated Production API Key",
  "note": "Updated for new integration"
}
```

### Revoke API Key
```http
DELETE /api/PartnerApiKeys/{keyId}
Authorization: Bearer {jwt_token}
```

## Migration Notes

- Moved from `/api/api-clients/api-keys` to `/api/PartnerApiKeys`
- Removed AutoMapper dependency for better performance
- Enhanced security with explicit partner context validation
- Added update and revoke functionality
- Improved error handling and validation
