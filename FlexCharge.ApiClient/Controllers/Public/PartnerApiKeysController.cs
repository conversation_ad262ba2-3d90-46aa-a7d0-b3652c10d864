using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.ApiClient.Services;
using FlexCharge.ApiClient.DTO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using FlexCharge.Common.Telemetry;

namespace FlexCharge.ApiClient.Controllers.Public
{
    [Route("api/[controller]")]
    [ApiController]
    [JwtAuth]
    public class PartnerApiKeysController : BaseController
    {
        private readonly IApiIdentityService _apiIdentityService;
        private readonly AppOptions _globalOptions;

        public PartnerApiKeysController(
            IOptions<AppOptions> globalOptions,
            IApiIdentityService apiIdentityService)
        {
            _globalOptions = globalOptions.Value;
            _apiIdentityService = apiIdentityService;
        }

        /// <summary>
        /// Get API keys for the authenticated partner
        /// </summary>
        /// <param name="pageSize">Number of items per page (default: 10, max: 100)</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="type">Optional filter by key type (e.g., "production", "sandbox")</param>
        /// <param name="revoked">Optional filter by revoked status</param>
        /// <returns>Paginated list of API keys for the partner</returns>
        [HttpGet]
        [Authorize(MyPolicies.PARTNER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(PartnerApiKeysResponseDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetApiKeys(
            [FromQuery] int pageSize = 10, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] string? type = null, 
            [FromQuery] bool? revoked = null)
        {
            using var workspan = Workspan.StartEndpoint<PartnerApiKeysController>(this, null, _globalOptions)
                .Baggage("pageSize", pageSize)
                .Baggage("pageNumber", pageNumber)
                .Baggage("type", type)
                .Baggage("revoked", revoked);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                // Validate pagination parameters
                if (pageSize <= 0 || pageSize > 100)
                {
                    return BadRequest("Page size must be between 1 and 100");
                }

                if (pageNumber <= 0)
                {
                    return BadRequest("Page number must be greater than 0");
                }

                var partnerId = GetPID();
                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                {
                    return BadRequest("Partner ID not found in token claims");
                }

                var apiKeys = await _apiIdentityService.GetPartnerApiKeys(
                    partnerId.Value,
                    pageSize,
                    pageNumber,
                    type,
                    revoked);

                if (!apiKeys.Success)
                    return ReturnResponse(apiKeys);

                workspan.Log.Information($"Partner {partnerId} accessed API keys list - Page: {pageNumber}, Size: {pageSize}");
                return Ok(apiKeys);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        /// <summary>
        /// Create a new API key for the authenticated partner
        /// </summary>
        /// <param name="payload">API key creation request</param>
        /// <returns>Created API key with the secret value (shown only once)</returns>
        [HttpPost]
        [Authorize(MyPolicies.PARTNER_ADMINS_ONLY)]
        [ProducesResponseType(typeof(ApiKeyGenerationResult), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CreateApiKey([FromBody] PartnerApiKeyCreateDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<PartnerApiKeysController>(this, payload, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var partnerId = GetPID();
                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                {
                    return BadRequest("Partner ID not found in token claims");
                }

                var userId = GetUID();

                // Validate that the client ID belongs to the authenticated partner if provided
                if (payload.ClientId.HasValue)
                {
                    var hasAccess = await _apiIdentityService.ValidatePartnerClientAccess(partnerId.Value, payload.ClientId.Value);
                    if (!hasAccess)
                    {
                        workspan.Log.Warning($"Partner {partnerId} attempted to create API key for unauthorized client {payload.ClientId}");
                        return Forbid("Access denied: Client does not belong to your partner account");
                    }
                }

                var result = await _apiIdentityService.CreatePartnerApiKey(
                    partnerId.Value,
                    userId,
                    payload);

                if (!result.Success)
                    return ReturnResponse(result);

                workspan.Log.Information($"Partner {partnerId} created new API key for client {payload.ClientId}");
                return CreatedAtAction(nameof(GetApiKeys), new { }, result);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        /// <summary>
        /// Update an existing API key (description, note, type)
        /// </summary>
        /// <param name="keyId">API key ID</param>
        /// <param name="payload">Update request</param>
        /// <returns>Success response</returns>
        [HttpPut("{keyId:guid}")]
        [Authorize(MyPolicies.PARTNER_ADMINS_ONLY)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateApiKey(Guid keyId, [FromBody] PartnerApiKeyUpdateDTO payload)
        {
            using var workspan = Workspan.StartEndpoint<PartnerApiKeysController>(this, new { keyId, payload }, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var partnerId = GetPID();
                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                {
                    return BadRequest("Partner ID not found in token claims");
                }

                var result = await _apiIdentityService.UpdatePartnerApiKey(partnerId.Value, keyId, payload);

                if (!result.Success)
                    return ReturnResponse(result);

                workspan.Log.Information($"Partner {partnerId} updated API key {keyId}");
                return Ok(result);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        /// <summary>
        /// Revoke an API key
        /// </summary>
        /// <param name="keyId">API key ID to revoke</param>
        /// <returns>Success response</returns>
        [HttpDelete("{keyId:guid}")]
        [Authorize(MyPolicies.PARTNER_ADMINS_ONLY)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RevokeApiKey(Guid keyId)
        {
            using var workspan = Workspan.StartEndpoint<PartnerApiKeysController>(this, keyId, _globalOptions);

            try
            {
                if (!ModelState.IsValid) return ValidationProblem();

                var partnerId = GetPID();
                if (!partnerId.HasValue || partnerId.Value == Guid.Empty)
                {
                    return BadRequest("Partner ID not found in token claims");
                }

                var result = await _apiIdentityService.RevokePartnerApiKey(partnerId.Value, keyId);

                if (!result.Success)
                    return ReturnResponse(result);

                workspan.Log.Information($"Partner {partnerId} revoked API key {keyId}");
                return Ok(result);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }
    }
}
