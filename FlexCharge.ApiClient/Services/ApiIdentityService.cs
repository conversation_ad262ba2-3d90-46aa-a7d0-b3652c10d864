using AutoMapper;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Dependencies;
using FlexCharge.Common.Emails;
using FlexCharge.Common.Sms;
using FlexCharge.ApiClient;
using FlexCharge.ApiClient.DTO;
using FlexCharge.ApiClient.Entities;
using FlexCharge.ApiClient.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO.Pipes;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using FlexCharge.Common;
using FlexCharge.Common.Authentication.BasicAuthentication;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Types;
using FlexCharge.Contracts;
using MassTransit;

namespace FlexCharge.ApiClient.Services
{
    public class ApiIdentityService : IApiIdentityService
    {
        private readonly IJwtHandler _basicAuthenticationHandler;
        private readonly IApiKeyService _apikeyService;
        private readonly IClaimsProvider _claimsProvider;
        private readonly IMapper _mapper;
        private readonly PostgreSQLDbContext _dbContext;
        private readonly IPublishEndpoint _publisher;
        private readonly IPasswordHasher<Entities.ApiClient> _passwordHasher;

        public ApiIdentityService(
            IJwtHandler basicAuthenticationHandler,
            IApiKeyService apikeyService,
            IClaimsProvider claimsProvider,
            IMapper mapper,
            IPasswordHasher<Entities.ApiClient> passwordHasher,
            PostgreSQLDbContext dbContext,
            IPublishEndpoint publisher)
        {
            _basicAuthenticationHandler = basicAuthenticationHandler;
            _apikeyService = apikeyService;
            //_refreshTokenRepository = refreshTokenRepository;
            //_refreshTokenService = refreshTokenService;
            _claimsProvider = claimsProvider;
            _mapper = mapper;
            _passwordHasher = passwordHasher;
            _dbContext = dbContext;
            _publisher = publisher;
        }

        public async Task<JsonWebToken> Authenticate(string appKey, string appSecret)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("AppKey", appKey);

            var response = new JsonWebToken();

            try
            {
                var result = await _apikeyService.Verify(appKey, appSecret);
                if (result.verificationResult == ApiKeyService.ApiKeyVerificationResult.Failed)
                {
                    response.AddError(Consts.ApiKeyError);
                    return response;
                }

                var apiResources = await _dbContext.ApiResources.Select(x => x.Name).ToArrayAsync();
                var apiClientClaims = await _claimsProvider.GetApiClientClaimsAsync(result.apiClient.Id);
                //var claims = await _claimsProvider.GetApiScopesClaimsAsync();

                var mid = apiClientClaims.FirstOrDefault(x => x.Type == MyClaimTypes.MERCHANT_ID)?.Value;
                var pid = apiClientClaims.FirstOrDefault(x => x.Type == MyClaimTypes.PARTNER_ID)?.Value;

                var isPartner = !string.IsNullOrEmpty(pid) &&
                                (string.IsNullOrEmpty(mid) || pid == Guid.Empty.ToString());
                var expires = isPartner ? DateTime.Now.AddYears(10) : DateTime.Now.AddMinutes(30);

                response = _basicAuthenticationHandler.CreateToken(result.apiClient.Id.ToString(), apiResources, null,
                    apiClientClaims, expires);
                response.RefreshToken = CreateTokenForApiClient(result.apiClient, _passwordHasher);

                //Revoke old refresh tokens because a new login was performed
                //await _refreshTokenService.RevokeAllClientsAsync(result.apiClient.Id);
                //Add newly created token to db
                //await _refreshTokenRepository.AddAsync(refreshToken);

                return response;
            }
            catch (FormatException e)
            {
                workspan.RecordFatalException(e);
                response.AddError("Keys validation failed either AppKey or AppSecret is invalid", "ApiKeys");
            }

            return response;
        }

        private static string CreateTokenForApiClient(Entities.ApiClient client,
            IPasswordHasher<Entities.ApiClient> passwordHasher)
            => passwordHasher.HashPassword(client, Guid.NewGuid().ToString("N"))
                .Replace("=", string.Empty)
                .Replace("+", string.Empty)
                .Replace("/", string.Empty);

        public async Task<ApiKeyGenerationResult> AdminGenerateKey(Guid? mid, Guid? pid, Guid? userId, int expiration,
            string? note,
            string description = "Default Keys")
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Mid", mid)
                .Baggage("Pid", pid)
                .Baggage("UserId", userId)
                .Baggage("Expiration", expiration)
                .Baggage("Note", note)
                .Baggage("Description", description)
                .LogEnterAndExit();

            ApiKeyGenerationResult response = new ApiKeyGenerationResult();

            try
            {
                var apiClient = await this.CreateApiClient(userId, mid, pid, new ApiClientRequest
                {
                    Name = description,
                    UniqueName = description,
                    Description = description,
                    Uri = new Uri("https://www.flexfactor.io")
                });

                workspan
                    .Tag("ApiClient", apiClient);

                if (apiClient.ClientId == Guid.Empty)
                {
                    workspan.Log.Error("ERROR Creating apiClient");
                    response.AddError(Consts.ApiKeyError);
                    return response;
                }

                var result =
                    await _apikeyService.Generate(apiClient.ClientId, description, TimeSpan.FromMinutes(expiration),
                        note);


                if (string.IsNullOrEmpty(result.ApiClientSecret.Key) ||
                    string.IsNullOrEmpty(result.ApiClientSecret.Value))
                {
                    workspan.Log.Error("ERROR Generating _apikeyService");
                    response.AddError(Consts.ApiKeyError);

                    return response;
                }

                response.ClientId = result.ApiClientSecret.Key;
                response.Secret = result.ApiClientSecret.Value;

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                response.AddError(e.Message);
                return response;
            }
        }


        public async Task<ApiKeyGenerationResult> GenerateKey(int expiration, string? note,
            string description = "Default Keys"
        )
        {
            ApiKeyGenerationResult response = new ApiKeyGenerationResult();

            try
            {
                var result = await _apikeyService.Generate(Guid.NewGuid(), description,
                    expiration == 0 ? TimeSpan.Zero : TimeSpan.FromMinutes(expiration), note);

                if (string.IsNullOrEmpty(result.ApiClientSecret.Key) ||
                    string.IsNullOrEmpty(result.ApiClientSecret.Value))
                {
                    response.AddError(Consts.ApiKeyError);
                    return response;
                }

                response.ClientId = result.ApiClientSecret.Key;
                response.Secret = result.ApiClientSecret.Value;

                return response;
            }
            catch (Exception e)
            {
                response.AddError(e.Message);
                return response;
            }
        }

        public async Task<ApiClientResult> CreateApiClient(Guid? uid, Guid? mid, Guid? pid, ApiClientRequest payload)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Uid", uid)
                .Baggage("Mid", mid)
                .Baggage("Pid", pid)
                .Payload(payload)
                .LogEnterAndExit();

            var response = new ApiClientResult();

            try
            {
                //create if not exists
                // var client = await _dbContext.ApiClients
                //     .FirstOrDefaultAsync(x => x.Mid == mid);
                //
                // if (client != null)
                // {
                //     response.ClientId = client.Id;
                //     return response;
                // }

                var newClient = new Entities.ApiClient
                {
                    Name = payload.Name,
                    UniqueName = payload.Description,
                    Description = payload.Description,
                    Mid = mid,
                    Pid = pid,
                    Uri = payload.Uri,
                    UserId = uid
                };

                await _dbContext.AddAsync(newClient);


                if (pid.HasValue && pid.Value != Guid.Empty)
                {
                    workspan.Log.Information(
                        "IN: ApiIdentityService > Adding Claim to ApiClientClaims");

                    _dbContext.ApiClientClaims.Add(new ApiClientClaim
                    {
                        Value = pid.ToString(),
                        Type = MyClaimTypes.PARTNER_ID,
                        ApiClientId = newClient.Id
                    });
                }

                if (mid != Guid.Empty)
                {
                    workspan.Log.Information(
                        "IN: ApiIdentityService > Adding Claim to ApiClientClaims");

                    _dbContext.ApiClientClaims.Add(new ApiClientClaim
                    {
                        Value = mid.ToString(),
                        Type = MyClaimTypes.MERCHANT_ID,
                        ApiClientId = newClient.Id
                    });

                    if (payload.Claims != null)
                    {
                        try
                        {
                            foreach (var claim in payload.Claims)
                            {
                                _dbContext.ApiClientClaims.Add(new ApiClientClaim
                                {
                                    Value = claim.Value,
                                    Type = claim.Key,
                                    ApiClientId = newClient.Id
                                });
                            }
                        }
                        catch (Exception e)
                        {
                            workspan.Log.Fatal("Error adding claims to ApiClientClaims");
                        }
                    }
                }

                workspan
                    .Response(response);

                await _dbContext.SaveChangesAsync();

                await _publisher.Publish<ApiClientCreatedEvent>(new
                {
                    Id = newClient.Id
                });

                //TODO add api client claims/scopes

                response.ClientId = newClient.Id;
                return response;
            }
            catch (Exception e)
            {
                await _publisher.Publish<ApiClientCreateFailedEvent>(new
                {
                    Message = JsonConvert.SerializeObject(e)
                });

                workspan.RecordException(e);
                throw;
            }
        }

        public async Task RevokeKey(Guid? uid, Guid mid, Guid keyId)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Uid", uid)
                .Baggage("Mid", mid)
                .Baggage("KeyId", keyId)
                .LogEnterAndExit();

            try
            {
                var keyToRevoke = await _dbContext.ApiClientSecrets.Include(x => x.Client)
                    .Where(x => x.Client.Mid == mid && !x.RevokedAt.HasValue && x.Id == keyId)
                    .SingleOrDefaultAsync();

                ArgumentNullException.ThrowIfNull(keyToRevoke);

                await _apikeyService.RevokeAsync(keyToRevoke.Key, keyToRevoke.ClientId);
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<ApiKeysResult> GetKeys(Guid? mid, Guid? pid, Guid? userId)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Mid", mid)
                .Baggage("UserId", userId)
                .LogEnterAndExit();

            var response = new ApiKeysResult();

            try
            {
                var keys = _dbContext.ApiClientSecrets
                    .Include(x => x.Client)
                    .Where(x => !x.RevokedAt.HasValue)
                    .AsQueryable();

                if (mid != null && mid != Guid.Empty && (pid == null || pid == Guid.Empty))
                {
                    keys = keys.Where(x => x.Client.Mid == mid);
                }
                else if (mid != null && mid != Guid.Empty && pid != null && pid != Guid.Empty)
                {
                    keys = keys.Where(x => x.Client.Mid == mid && x.Client.Pid == pid);
                }
                else if (pid != null && pid != Guid.Empty)
                {
                    keys = keys.Where(x => x.Client.Pid == pid && (x.Client.Mid == null || x.Client.Mid == Guid.Empty));
                }
                else
                {
                    response.AddError("No keys found");

                    return response;
                }

                // if (userId != Guid.Empty)
                //     keys = keys.Where(x => x.Client.UserId == userId);

                response.Keys = await keys.Select(key => new ApiKeyDTO
                {
                    //Name = key.Description,
                    Key = key.Key,
                    Secret = key.Value
                }).ToListAsync();

                if (response.Keys.Count() == 0)
                {
                    response.AddError("No keys found");
                }

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<ApiClientKeysResponseDTO> GetApiClientKeys(int pageSize, int pageNumber, Guid? pid)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .LogEnterAndExit();

            var response = new ApiClientKeysResponseDTO();

            try
            {
                var dbset = _dbContext.ApiClientSecrets
                    .Include(x => x.Client)
                    .Where(x => !x.IsDeleted)
                    .OrderByDescending(x => x.CreatedOn)
                    .AsQueryable();

                if (pid.HasValue)
                {
                    dbset = dbset.Where(
                        x => x.Client.Pid == pid && (x.Client.Mid == null || x.Client.Mid == Guid.Empty));
                }

                var apiClientKeys = await dbset
                    .GroupJoin(_dbContext.ApiClientClaims, x => x.ClientId, y => y.ApiClientId,
                        (apiClientSecret, apiClientClaim) => new {apiClientSecret, apiClientClaim})
                    .Select(x => new ApiClientSecretDTO
                    {
                        Id = x.apiClientSecret.Id,
                        Description = x.apiClientSecret.Description,
                        Key = x.apiClientSecret.Key,
                        Value = x.apiClientSecret.Value,
                        Expiration = x.apiClientSecret.Expiration,
                        Type = x.apiClientSecret.Type,
                        ClientId = x.apiClientSecret.ClientId,
                        LastUsed = x.apiClientSecret.LastUsed,
                        Note = x.apiClientSecret.Note,
                        Scopes = x.apiClientClaim
                            .Where(x => x.ApiScopeId != null)
                            .Select(y => y.ApiScopeId.Value)
                            .ToList()
                    })
                    .ToPagedListAsync(pageNumber, pageSize);

                response.ApiClients =
                    _mapper
                        .Map<IPagedList<ApiClientSecretDTO>,
                            PagedDTO<ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO>>(
                            apiClientKeys);

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO> GetApiClientKey(Guid id)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("Id", id)
                .LogEnterAndExit();

            try
            {
                var apiClientKeys = await _dbContext.ApiClientSecrets
                    .Include(x => x.Client)
                    .FirstOrDefaultAsync(x => x.Id == id);

                var scopes = await _dbContext.ApiClientClaims
                    .Where(x => x.IsDeleted == false && x.ApiClientId == apiClientKeys.ClientId && x.ApiScopeId != null)
                    .Select(x => x.ApiScopeId.Value)
                    .ToListAsync();

                var response = new ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO
                {
                    Id = apiClientKeys.Id,
                    Description = apiClientKeys.Description,
                    Key = apiClientKeys.Key,
                    Value = null, // Always exclude Value for security - only shown during creation
                    Expiration = apiClientKeys.Expiration,
                    Type = apiClientKeys.Type,
                    ClientId = apiClientKeys.ClientId,
                    LastUsed = apiClientKeys.LastUsed,
                    Note = apiClientKeys.Note,
                    Scopes = scopes
                };

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<ApiClientKeysResponseDTO> GetPartnerApiKeys(Guid partnerId, int pageSize, int pageNumber, string? type = null, bool? revoked = null)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("PartnerId", partnerId)
                .Baggage("PageSize", pageSize)
                .Baggage("PageNumber", pageNumber)
                .Baggage("Type", type)
                .Baggage("Revoked", revoked)
                .LogEnterAndExit();

            var response = new ApiClientKeysResponseDTO();

            try
            {
                var dbset = _dbContext.ApiClientSecrets
                    .Include(x => x.Client)
                    .Where(x => !x.IsDeleted && x.Client.Pid == partnerId)
                    .OrderByDescending(x => x.CreatedOn)
                    .AsQueryable();

                // Apply optional filters
                if (!string.IsNullOrEmpty(type))
                {
                    dbset = dbset.Where(x => x.Type == type);
                }

                if (revoked.HasValue)
                {
                    if (revoked.Value)
                    {
                        dbset = dbset.Where(x => x.RevokedAt.HasValue);
                    }
                    else
                    {
                        dbset = dbset.Where(x => !x.RevokedAt.HasValue);
                    }
                }

                var apiClientKeys = await dbset
                    .GroupJoin(_dbContext.ApiClientClaims, x => x.ClientId, y => y.ApiClientId,
                        (apiClientSecret, apiClientClaim) => new {apiClientSecret, apiClientClaim})
                    .Select(x => new ApiClientSecretDTO
                    {
                        Id = x.apiClientSecret.Id,
                        Description = x.apiClientSecret.Description,
                        Key = x.apiClientSecret.Key,
                        Value = null, // Always exclude Value for security
                        Expiration = x.apiClientSecret.Expiration,
                        Type = x.apiClientSecret.Type,
                        ClientId = x.apiClientSecret.ClientId,
                        LastUsed = x.apiClientSecret.LastUsed,
                        Note = x.apiClientSecret.Note,
                        Scopes = x.apiClientClaim
                            .Where(x => x.ApiScopeId != null)
                            .Select(y => y.ApiScopeId.Value)
                            .ToList()
                    })
                    .ToPagedListAsync(pageNumber, pageSize);

                response.ApiClients =
                    _mapper
                        .Map<IPagedList<ApiClientSecretDTO>,
                            PagedDTO<ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO>>(
                            apiClientKeys);

                // Ensure Value is always null in the response
                foreach (var item in response.ApiClients.rows)
                {
                    item.Value = null;
                }

                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }

        public async Task<ApiKeyGenerationResult> CreatePartnerApiKey(Guid partnerId, Guid userId, PartnerApiKeyCreateDTO payload)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("PartnerId", partnerId)
                .Baggage("UserId", userId)
                .Baggage("ClientId", payload.ClientId)
                .Baggage("Description", payload.Description)
                .Baggage("Expiration", payload.Expiration)
                .LogEnterAndExit();

            var response = new ApiKeyGenerationResult();

            try
            {
                Guid clientId;

                // If client ID is provided, validate it belongs to the partner
                if (payload.ClientId.HasValue)
                {
                    var hasAccess = await ValidatePartnerClientAccess(partnerId, payload.ClientId.Value);
                    if (!hasAccess)
                    {
                        response.AddError("Access denied: Client does not belong to your partner account");
                        return response;
                    }
                    clientId = payload.ClientId.Value;
                }
                else
                {
                    // Create a new API client for the partner
                    var apiClientResult = await CreateApiClient(userId, null, partnerId, new ApiClientRequest
                    {
                        Name = payload.Description,
                        UniqueName = $"Partner-{partnerId}-{Guid.NewGuid()}",
                        Description = payload.Description,
                        Uri = new Uri("https://www.flexfactor.io"),
                        Note = payload.Note
                    });

                    if (apiClientResult.ClientId == Guid.Empty)
                    {
                        workspan.Log.Error("ERROR Creating apiClient for partner");
                        response.AddError("Failed to create API client");
                        return response;
                    }

                    clientId = apiClientResult.ClientId;
                }

                // Generate the API key
                var expiration = payload.Expiration == 0 ? TimeSpan.Zero : TimeSpan.FromMinutes(payload.Expiration);
                var result = await _apikeyService.Generate(clientId, payload.Description, expiration, payload.Note);

                if (string.IsNullOrEmpty(result.ApiClientSecret.Key) || string.IsNullOrEmpty(result.ApiClientSecret.Value))
                {
                    workspan.Log.Error("ERROR Generating API key for partner");
                    response.AddError("Failed to generate API key");
                    return response;
                }

                // Set the type if provided
                if (!string.IsNullOrEmpty(payload.Type))
                {
                    var apiClientSecret = await _dbContext.ApiClientSecrets
                        .FirstOrDefaultAsync(x => x.Key == result.ApiClientSecret.Key);
                    if (apiClientSecret != null)
                    {
                        apiClientSecret.Type = payload.Type;
                        _dbContext.ApiClientSecrets.Update(apiClientSecret);
                        await _dbContext.SaveChangesAsync();
                    }
                }

                // Add scopes if provided
                if (payload.Scopes != null && payload.Scopes.Any())
                {
                    var scopeClaims = payload.Scopes.Select(scopeId => new ApiClientClaim
                    {
                        Value = scopeId.ToString(),
                        Type = MyClaimTypes.SCOPE,
                        ApiClientId = clientId,
                        ApiScopeId = scopeId
                    }).ToList();

                    _dbContext.ApiClientClaims.AddRange(scopeClaims);
                    await _dbContext.SaveChangesAsync();
                }

                response.ClientId = result.ApiClientSecret.Key;
                response.Secret = result.ApiClientSecret.Value;

                workspan.Log.Information($"Successfully created API key for partner {partnerId}");
                return response;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                response.AddError(e.Message);
                return response;
            }
        }

        public async Task<bool> ValidatePartnerClientAccess(Guid partnerId, Guid clientId)
        {
            using var workspan = Workspan.Start<ApiIdentityService>()
                .Baggage("PartnerId", partnerId)
                .Baggage("ClientId", clientId)
                .LogEnterAndExit();

            try
            {
                var client = await _dbContext.ApiClients
                    .FirstOrDefaultAsync(x => x.Id == clientId && x.Pid == partnerId && !x.IsDeleted);

                return client != null;
            }
            catch (Exception e)
            {
                workspan.RecordException(e);
                throw;
            }
        }
    }
}