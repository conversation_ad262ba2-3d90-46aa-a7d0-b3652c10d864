using FlexCharge.ApiClient.DTO;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Response;

namespace FlexCharge.ApiClient.Services;

public interface IApiIdentityService
{
    Task<JsonWebToken> Authenticate(string appKey, string appSecret);
    Task<ApiClientResult> CreateApiClient(Guid? userId, Guid? mid, Guid? partnerId, ApiClientRequest payload);
    Task<ApiKeysResult> GetKeys(Guid? mid, Guid? pid, Guid? userId);

    Task<ApiKeyGenerationResult> AdminGenerateKey(Guid? mid, Guid? pid, Guid? userId, int expiration, string? note,
        string description = "Default Keys");

    Task<ApiKeyGenerationResult> GenerateKey(int expiration, string? note, string description);
    Task RevokeKey(Guid? userId, Guid mid, Guid keyId);
    Task<ApiClientKeysResponseDTO> GetApiClientKeys(int pageSize, int pageNumber, Guid? pid);
    Task<ApiClientKeysResponseDTO.ApiClientKeyItemQueryDTO> GetApiClientKey(Guid id);

    // Partner-specific methods for CORE-3729
    Task<PartnerApiKeysResponseDTO> GetPartnerApiKeys(Guid partnerId, int pageSize, int pageNumber, string? type = null, bool? revoked = null);
    Task<ApiKeyGenerationResult> CreatePartnerApiKey(Guid partnerId, Guid userId, PartnerApiKeyCreateDTO payload);
    Task<SimpleResponse> UpdatePartnerApiKey(Guid partnerId, Guid keyId, PartnerApiKeyUpdateDTO payload);
    Task<SimpleResponse> RevokePartnerApiKey(Guid partnerId, Guid keyId);
    Task<bool> ValidatePartnerClientAccess(Guid partnerId, Guid clientId);
}