using FlexCharge.ApiClient;
using FlexCharge.ApiClient.Entities;
using FlexCharge.ApiClient.Services;
using FlexCharge.ApiClient.Services.ProviderOAuthService;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Cloud.BI.Amazon;
using FlexCharge.Common.Logging;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Swagger;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Shared.Authentication.OAuth;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.PerformanceCounters;

var builder = WebApplication.CreateBuilder();

builder.Configuration
    .SetBasePath(builder.Environment.ContentRootPath)
    .AddJsonFile("appsettings.json", true, true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", true, true)
    .AddEnvironmentVariables();

bool enableTelemetryConsoleExporter = false;

#region For OpenTelemetry troubleshooting purposes only

// if (EnvironmentHelper.IsInSandboxOrStagingOrDevelopment)
// {
//     enableTelemetryConsoleExporter = true;
// }

#endregion

builder.Services.AddTelemetry(enableTelemetryConsoleExporter);

builder.Services.AddCloudWatchPerformanceCountersTelemetry<Program>();

//builder.Services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));
builder.Services.AddOptions();

builder.Services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddTransient<IApiIdentityService, ApiIdentityService>();
builder.Services.AddTransient<IApiKeyService, ApiKeyService>();
builder.Services.AddTransient<IJwtHandler, BasicOauthJwtHandler>();
builder.Services.AddTransient<IClaimsProvider, ClaimsProvider>();
builder.Services.AddTransient<IPasswordHasher<ApiClient>, PasswordHasher<ApiClient>>();


builder.Services.AddActivities();

builder.Services.AddOAuthClientHandlers<ProviderOAuthService>();

builder.Services.AddAmazonSecretsManager();


// Add services to the container.
builder.Services.AddControllers();

builder.Services.AddMassTransit<Program>();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
// builder.Services.AddEndpointsApiExplorer();
// builder.Services.AddSwaggerGen();

builder.Services.AddRedisCache();

builder.Services.AddJwt();

var connectionString =
    $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
connectionString = "Host=localhost;Database=fc.apiclient;Username=apiclient-service-staging;Password=*****";
#endif

builder.Services
    .AddNpgsqlDbContext<PostgreSQLDbContext>(
        connectionString,
        npgsqlOptionsAction: options => options.EnableRetryOnFailure(3));
//.AddDbContext<PostgreSQLDbContext>(options => options.UseNpgsql("Host=localhost;Database=fc.apiclient;Username=apiclient-service-staging;Password=*****"));

// AutoMapper removed - using manual mapping for better control and performance
builder.Services.AddSwaggerDocs();


builder.Services.AddAuthorization(options =>
    {
        options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
            policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));

        options.AddPolicy(MyPolicies.MERCHANT_ADMINS,
            policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.MERCHANT_ADMIN));

        options.AddPolicy(MyPolicies.PARTNER_ADMINS_ONLY,
            policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.PARTNER_ADMIN));
    }
);

builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy", cors =>
        cors.AllowAnyMethod()
            .AllowAnyOrigin()
            .AllowAnyHeader());
});

builder.Host.UseLogging();

var app = builder.Build();

app.UseRouting();
app.UseCors("CorsPolicy");
// Configure the HTTP request pipeline.
// if (app.Environment.IsDevelopment())
// {
//
// }
app.UseSwaggerDocs();


app.UseAutoMigrations<PostgreSQLDbContext>();

#if !DEBUG
app.UseHttpsRedirection();
#endif

app.UseAuthorization();


app.MapControllers();

app.UseMassTransit();

app.UseOAuthClientHandlers();

app.Run();