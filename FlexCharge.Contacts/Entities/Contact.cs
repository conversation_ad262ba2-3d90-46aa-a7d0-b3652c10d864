using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace FlexCharge.Contacts.Entities
{
    public class Contact : AuditableEntity
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string SecondaryEmail { get; set; }
        public string Phone { get; set; }
        public Guid? UserId { get; set; }
        public string ProfileUrl { get; set; }
        
        //driver license
        public string? DriverLicenseNumber { get; set; }
        public string? DriverLicenseState { get; set; }
        public string? DriverLicenseCountry { get; set; }
        public DateTime? DriverLicenseExpirationDate { get; set; }
        
        //passport
        public string? PassportNumber { get; set; }
        public string? PassportCountry { get; set; }
        public DateTime? PassportExpirationDate { get; set; }
        
        // Patron information
        public string? PatronId { get; set; }
        public string? PatronType { get; set; }
        public string? PatronStatus { get; set; }
        public string? PatronDescription { get; set; }
        public string? PatronLanguage { get; set; }
        
        public List<xContactAccounts> ContactAccounts { get; set; }
        [Column(TypeName = "jsonb")] public string? Meta { get; set; }
        public bool? IsExternal { get; set; }
        public string Currency { get; set; }
    }

}
