<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>FlexCharge.Eligibility.Adapters.Stripe</RootNamespace>
        <PackageId>FlexCharge.Eligibility.Adapters.Stripe</PackageId>
        <WarningsAsErrors>CS4014</WarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj"/>
        <ProjectReference Include="..\FlexCharge.Eligibility.Adapters.Common\FlexCharge.Eligibility.Adapters.Common.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Stripe.net" Version="47.3.0"/>
    </ItemGroup>

</Project>
