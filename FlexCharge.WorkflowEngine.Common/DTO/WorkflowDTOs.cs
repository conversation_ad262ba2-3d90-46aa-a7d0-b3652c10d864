using System;
using System.Collections.Generic;
using FlexCharge.Common.AutoMapper;
using FlexCharge.Common.Response;
using FlexCharge.WorkflowEngine.Common.Workflows.Designer;
using FlexCharge.WorkflowEngine.Common.Workflows.Designer.Model;
using FlexCharge.WorkflowEngine.Entities;

namespace FlexCharge.WorkflowEngine.DTO;

public class WorkflowReponseDTO
{
    public Guid Id { get; set; }
    public string Name { get; set; }

    public WorkflowDefinition WorkflowDefinition { get; set; }
}

public class WorkflowsQueryResponse : BaseResponse
{
    public WorkflowsQueryResponse()
    {
        WorkflowStatuses = new Dictionary<int, string>();

        foreach (WorkflowStatuses status in Enum.GetValues(typeof(WorkflowStatuses)))
        {
            var statusName = status.ToString();
            WorkflowStatuses.Add((int) status, char.ToUpper(statusName[0]) + statusName.Substring(1));
        }
    }

    public PagedDTO<WorkflowQueryDTO> Workflows { get; set; }
    public Dictionary<int, string> WorkflowStatuses { get; set; }

    public class WorkflowQueryDTO
    {
        public Guid Id { get; set; }
        public Guid WorkflowId { get; set; }
        public string Name { get; set; }

        // public WorkflowDefinition WorkflowDefinition { get; set; }

        public string Description { get; set; }
        public int Version { get; set; }
        public string Domain { get; set; }
        public DateTime? LastRun { get; set; }
        public string Status { get; set; }
        public List<WorkflowItemQueryDTO> WorkflowItems { get; set; }
        public int? PublishedVersion { get; set; }
    }

    public class WorkflowItemQueryDTO
    {
        public Guid Id { get; set; }
        public Guid WorkflowId { get; set; }
        public string Name { get; set; }

        public string Description { get; set; }
        public int Version { get; set; }
        public string Status { get; set; }
        public string Domain { get; set; }
        public DateTime CreatedOn { get; set; }
        public string ModifiedBy { get; set; }
    }
}

public class WorkflowCreateDTO
{
    public string Name { get; set; }
    public string Description { get; set; }
    public string Domain { get; set; }
}

public class WorkflowUpdateDTO
{
    public Guid Id { get; set; }
    public string? Name { get; set; }

    public WorkflowDefinition? WorkflowDefinition { get; set; }

    public string? Description { get; set; }
    public string? Status { get; set; }
    public string? Domain { get; set; }
}

public class WorkflowSaveDTO
{
    public Guid Id { get; set; }
    public Guid WorkflowId { get; set; }
    public string? Name { get; set; }
    public WorkflowDefinition? WorkflowDefinition { get; set; }
    public string? Description { get; set; }
    public string? Status { get; set; }
    public string Domain { get; set; }
    public int Version { get; set; }
}