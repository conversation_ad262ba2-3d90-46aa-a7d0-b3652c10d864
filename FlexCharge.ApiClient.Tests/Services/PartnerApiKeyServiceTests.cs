using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.ApiClient.DTO;
using FlexCharge.ApiClient.Entities;
using FlexCharge.ApiClient.Services;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Contracts;
using MassTransit;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace FlexCharge.ApiClient.Tests.Services
{
    public class PartnerApiKeyServiceTests : IDisposable
    {
        private readonly PostgreSQLDbContext _dbContext;
        private readonly ApiIdentityService _apiIdentityService;
        private readonly Guid _testPartnerId = Guid.NewGuid();
        private readonly Guid _testUserId = Guid.NewGuid();

        public PartnerApiKeyServiceTests()
        {
            var services = new ServiceCollection();

            // Setup in-memory database
            services.AddDbContext<PostgreSQLDbContext>(options =>
                options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

            // AutoMapper removed - using manual mapping
//
//             // Setup other dependencies
//             services.AddScoped<IPasswordHasher<Entities.ApiClient>, PasswordHasher<Entities.ApiClient>>();
//             services.AddScoped<IApiKeyService, ApiKeyService>();
//             services.AddScoped<IJwtHandler, MockJwtHandler>();
//             services.AddScoped<IClaimsProvider, MockClaimsProvider>();
//             services.AddScoped<IPublishEndpoint, MockPublishEndpoint>();
//
//             var serviceProvider = services.BuildServiceProvider();
//             _dbContext = serviceProvider.GetRequiredService<PostgreSQLDbContext>();
//
//             var mapper = serviceProvider.GetRequiredService<IMapper>();
//             var passwordHasher = serviceProvider.GetRequiredService<IPasswordHasher<Entities.ApiClient>>();
//             var apiKeyService = serviceProvider.GetRequiredService<IApiKeyService>();
//             var jwtHandler = serviceProvider.GetRequiredService<IJwtHandler>();
//             var claimsProvider = serviceProvider.GetRequiredService<IClaimsProvider>();
//             var publishEndpoint = serviceProvider.GetRequiredService<IPublishEndpoint>();
//
//             _apiIdentityService = new ApiIdentityService(
//                 jwtHandler,
//                 apiKeyService,
//                 claimsProvider,
//                 mapper,
//                 passwordHasher,
//                 _dbContext,
//                 publishEndpoint);
//         }
//
//         [Fact]
//         public async Task ValidatePartnerClientAccess_WithValidPartnerAndClient_ReturnsTrue()
//         {
//             // Arrange
//             var clientId = Guid.NewGuid();
//             var apiClient = new Entities.ApiClient
//             {
//                 Id = clientId,
//                 Pid = _testPartnerId,
//                 Name = "Test Client",
//                 Description = "Test Description",
//                 IsDeleted = false
//             };
//
//             _dbContext.ApiClients.Add(apiClient);
//             await _dbContext.SaveChangesAsync();
//
//             // Act
//             var result = await _apiIdentityService.ValidatePartnerClientAccess(_testPartnerId, clientId);
//
//             // Assert
//             Assert.True(result);
//         }
//
//         [Fact]
//         public async Task ValidatePartnerClientAccess_WithInvalidPartner_ReturnsFalse()
//         {
//             // Arrange
//             var clientId = Guid.NewGuid();
//             var differentPartnerId = Guid.NewGuid();
//             var apiClient = new Entities.ApiClient
//             {
//                 Id = clientId,
//                 Pid = _testPartnerId,
//                 Name = "Test Client",
//                 Description = "Test Description",
//                 IsDeleted = false
//             };
//
//             _dbContext.ApiClients.Add(apiClient);
//             await _dbContext.SaveChangesAsync();
//
//             // Act
//             var result = await _apiIdentityService.ValidatePartnerClientAccess(differentPartnerId, clientId);
//
//             // Assert
//             Assert.False(result);
//         }
//
//         [Fact]
//         public async Task GetPartnerApiKeys_WithValidPartner_ReturnsFilteredResults()
//         {
//             // Arrange
//             var clientId = Guid.NewGuid();
//             var apiClient = new Entities.ApiClient
//             {
//                 Id = clientId,
//                 Pid = _testPartnerId,
//                 Name = "Test Client",
//                 Description = "Test Description",
//                 IsDeleted = false
//             };
//
//             var apiSecret = new ApiClientSecret
//             {
//                 Id = Guid.NewGuid(),
//                 ClientId = clientId,
//                 Description = "Test Key",
//                 Key = "test-key",
//                 Value = "test-value",
//                 Type = "production",
//                 IsDeleted = false
//             };
//
//             _dbContext.ApiClients.Add(apiClient);
//             _dbContext.ApiClientSecrets.Add(apiSecret);
//             await _dbContext.SaveChangesAsync();
//
//             // Act
//             var result = await _apiIdentityService.GetPartnerApiKeys(_testPartnerId, 10, 1);
//
//             // Assert
//             Assert.True(result.Success);
//             Assert.Single(result.ApiClients.Items);
//             Assert.Null(result.ApiClients.Items.First().Value); // Value should be null for security
//         }
//
//         public void Dispose()
//         {
//             _dbContext?.Dispose();
//         }
//     }
//
//     // Mock implementations for testing
//     public class MockJwtHandler : IJwtHandler
//     {
//         public JsonWebToken CreateToken(string userId, string[] aud, string[] roles = null, List<System.Security.Claims.Claim> claims = null, DateTime? exp = null)
//         {
//             return new JsonWebToken { AccessToken = "mock-token" };
//         }
//
//         public JsonWebTokenPayload GetTokenPayload(string accessToken)
//         {
//             return new JsonWebTokenPayload();
//         }
//     }
//
//     public class MockClaimsProvider : IClaimsProvider
//     {
//         public Task<List<System.Security.Claims.Claim>> GetClaimsAsync(Guid clientId)
//         {
//             return Task.FromResult(new List<System.Security.Claims.Claim>());
//         }
//     }
//
//     public class MockPublishEndpoint : IPublishEndpoint
//     {
//         public ConnectHandle ConnectPublishObserver(IPublishObserver observer) => throw new NotImplementedException();
//         public Task Publish<T>(T message, CancellationToken cancellationToken = default) where T : class => Task.CompletedTask;
//         public Task Publish<T>(T message, IPipe<PublishContext<T>> publishPipe, CancellationToken cancellationToken = default) where T : class => Task.CompletedTask;
//         public Task Publish<T>(T message, IPipe<PublishContext> publishPipe, CancellationToken cancellationToken = default) where T : class => Task.CompletedTask;
//         public Task Publish(object message, CancellationToken cancellationToken = default) => Task.CompletedTask;
//         public Task Publish(object message, Type messageType, CancellationToken cancellationToken = default) => Task.CompletedTask;
//         public Task Publish(object message, IPipe<PublishContext> publishPipe, CancellationToken cancellationToken = default) => Task.CompletedTask;
//         public Task Publish(object message, Type messageType, IPipe<PublishContext> publishPipe, CancellationToken cancellationToken = default) => Task.CompletedTask;
//         public Task Publish<T>(object values, CancellationToken cancellationToken = default) where T : class => Task.CompletedTask;
//         public Task Publish<T>(object values, IPipe<PublishContext<T>> publishPipe, CancellationToken cancellationToken = default) where T : class => Task.CompletedTask;
//         public Task Publish<T>(object values, IPipe<PublishContext> publishPipe, CancellationToken cancellationToken = default) where T : class => Task.CompletedTask;
//     }
// }
