<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
		<UserSecretsId>99bbd081-7f55-4d77-9823-a712a2c8e7e0</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<!--<DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>-->
		<Configurations>Debug;Release;Staging</Configurations>
		<WarningsAsErrors>CS4014</WarningsAsErrors>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>bin\$(Configuration)\$(AssemblyName).xml</DocumentationFile>
		<NoWarn>1701;1702;1591;1573;</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Adyen" Version="19.1.0"/>
		<PackageReference Include="AWS.EncryptionSDK" Version="3.0.0" />
		<PackageReference Include="CheckoutSDK.Extensions.Microsoft" Version="4.0.18" />
		<PackageReference Include="Dwolla.Client" Version="5.2.1" />
        <PackageReference Include="EntityFrameworkCore.Exceptions.PostgreSQL" Version="8.1.3"/>
		<PackageReference Include="Going.Plaid" Version="4.12.0" />
		<PackageReference Include="jose-jwt" Version="4.1.0" />
		<PackageReference Include="MediatR" Version="8.0.1" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.2" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.0-rc.2.23480.2" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.8" />
		<PackageReference Include="Nager.Country" Version="4.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Nuvei-NetSDK" Version="********" />
		<PackageReference Include="Stripe.net" Version="47.4.0"/>
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
        <PackageReference Include="System.Text.Json" Version="9.0.3"/>
		<PackageReference Include="Zip2City" Version="3.0.0"/>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj" />
		<ProjectReference Include="..\FlexCharge.Contracts\FlexCharge.Contracts.csproj" />
		<ProjectReference Include="..\FlexCharge.Utils\FlexCharge.Utils.csproj" />
		<ProjectReference Include="..\FlexCharge.WorkflowEngine.Common\FlexCharge.WorkflowEngine.Common.csproj"/>
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="Services\StripeService\**"/>
		<Compile Remove="Services\SettelementsReportService\**"/>
		<Compile Remove="Services\GatewayServices\**"/>
		<Compile Remove="Migrations\20230704133903_add-disputes-descriptor-and-more.cs"/>
		<Compile Remove="Migrations\20230704133903_add-disputes-descriptor-and-more.Designer.cs"/>
		<Compile Remove="Migrations\20230704114230_add-disputes-table.cs"/>
		<Compile Remove="Migrations\20230704114230_add-disputes-table.Designer.cs"/>
		<Compile Remove="Migrations\20231004062827_add-meta-column-to-disputes-db.cs"/>
		<Compile Remove="Migrations\20231004062827_add-meta-column-to-disputes-db.Designer.cs"/>
        <Compile Remove="Migrations\20240725133149_changed-TransactionStatus-column-types-to-string-table.cs"/>
        <Compile Remove="Migrations\20240725133149_changed-TransactionStatus-column-types-to-string-table.Designer.cs"/>
        <Compile Remove="Migrations\20240725133946_changed-TransactionStatus-column-types-to-string-table.cs"/>
        <Compile Remove="Migrations\20240725133946_changed-TransactionStatus-column-types-to-string-table.Designer.cs"/>
        <Compile Remove="Migrations\20240729094220_make-TransactionMonitoring-tables-Auditable.cs"/>
        <Compile Remove="Migrations\20240729094220_make-TransactionMonitoring-tables-Auditable.Designer.cs"/>
        <Compile Remove="Migrations\20240827134018_updatte-sftphost-ReportingConfiguration-tables.cs" />
        <Compile Remove="Migrations\20240827134018_updatte-sftphost-ReportingConfiguration-tables.Designer.cs" />
        <Compile Remove="Migrations\20241020141558_add-merchantNumber-to-dispute-items-table.cs" />
        <Compile Remove="Migrations\20241020141558_add-merchantNumber-to-dispute-items-table.Designer.cs" />
        <Compile Remove="Migrations\20241023074242_add-OpenItemsAlert-NewItemsAlert-EmailRecipient-table.cs" />
        <Compile Remove="Migrations\20241023074242_add-OpenItemsAlert-NewItemsAlert-EmailRecipient-table.Designer.cs" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Remove="Services\StripeService\**" />
	  <EmbeddedResource Remove="Services\SettelementsReportService\**" />
	  <EmbeddedResource Remove="Services\GatewayServices\**" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="Services\StripeService\**" />
	  <None Remove="Services\SettelementsReportService\**" />
	  <None Remove="Services\GatewayServices\**" />
	  <None Remove="Grpc\PaymentGrpcServer.proto" />
	</ItemGroup>

	<ItemGroup>
	  <Content Remove="Services\StripeService\**" />
	  <Content Remove="Services\SettelementsReportService\**" />
	  <Content Remove="Services\GatewayServices\**" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="Services\GatewayServices\**" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Remove="Services\GatewayServices\**" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="Services\GatewayServices\**" />
	</ItemGroup>

	<ItemGroup>
	  <Content Remove="Services\GatewayServices\**" />
	</ItemGroup>

	<ItemGroup>
        <None Remove="Resources\Lists\ResponseCodeMapping\Nmi.csv"/>
		<Content Include="Resources\Lists\ResponseCodeMapping\Providers\Dummy.csv">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="Resources\Lists\ResponseCodeMapping\Providers\Nmi.csv">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<None Remove="Resources\Lists\ResponseCodeMapping\Providers\Paysafe.csv"/>
		<Content Include="Resources\Lists\ResponseCodeMapping\Providers\Paysafe.csv">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<None Remove="Resources\Lists\ResponseCodeMapping\InternalResponseCodeMapping.csv"/>
		<None Remove="Resources\Lists\ResponseCodeMapping\Providers\Stripe.csv"/>
		<Content Include="Resources\Lists\ResponseCodeMapping\Providers\Stripe.csv">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

    <Choose>
		<When Condition=" '$(Configuration)'=='Staging' ">
			<ItemGroup>
				<Content Remove="appsettings.Development.json" />

				<!-- Other files you want to update in the scope of Debug -->
				<None Update="other_files">
					<CopyToOutputDirectory>Never</CopyToOutputDirectory>
				</None>
			</ItemGroup>
		</When>
		<When Condition=" '$(Configuration)'=='Development' ">
			<ItemGroup>
				<Content Remove="appsettings.Staging.json" />

				<!-- Other files you want to update in the scope of Debug -->
				<None Update="other_files">
					<CopyToOutputDirectory>Never</CopyToOutputDirectory>
				</None>
			</ItemGroup>
		</When>
	</Choose>

</Project>
