using System;
using System.Collections.Generic;
using System.Linq;
using Amazon.S3;
using Amazon.SecretsManager;
using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Correlation;
using FlexCharge.Common.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using FlexCharge.Common.Activities;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cloud.BI.Amazon;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.DistributedLock.Implementations.RedLock;
using FlexCharge.Common.Emails;
using FlexCharge.Common.GeoServices;
using FlexCharge.Common.Grpc;
using FlexCharge.Common.HateosLinks;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Telemetry;
using FlexCharge.Payments.Services;
using FlexCharge.Payments.Services.Merchants;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.SpreedlyService;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.Telemetry.PerformanceCounters;
using FlexCharge.Contracts.Commands;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Payments.Services.DisputeServices;
using FlexCharge.Payments.Services.OpenBanking;
using FlexCharge.Payments.Services.PaymentServices.Providers.Nuvei;
using FlexCharge.Payments.Services.SVBAchServices;
using FlexCharge.Payments.Services.TokenExService;
using Going.Plaid;
using Newtonsoft.Json;
using Nuvei;
using Environment = System.Environment;
using FlexCharge.Common.Reporting;
using FlexCharge.Common.SftpServices;
using FlexCharge.Common.Shared.Common;
using FlexCharge.Common.Shared.Csv.DataMaps;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Payments.Services.SDKs.Nmi;
using FlexCharge.Common.Telemetry.HttpRequests;
using FlexCharge.Payments.Authorization;
using FlexCharge.Payments.BinChecker.CardBrands;
using FlexCharge.Payments.Controllers.GatewayControllers;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Enums;
using FlexCharge.Payments.GRPC;
using FlexCharge.Payments.Services.AccountUpdater;
using FlexCharge.Payments.Services.AccountUpdater.VGS;
using FlexCharge.Payments.Services.AlertProvidersService;
using FlexCharge.Payments.Services.CheckoutDisputeService;
using FlexCharge.Payments.Services.DisputeServices.Chargeblast;
using FlexCharge.Payments.Services.DisputeServices.Fiserv;
using FlexCharge.Payments.Services.DisputeServices.Kount;
using FlexCharge.Payments.Services.DisputeServices.Merlink;
using FlexCharge.Payments.Services.DisputeServices.Models;
using FlexCharge.Payments.Services.DisputeServices.MyRcvr;
using FlexCharge.Payments.Services.FinancialAccounts;
using FlexCharge.Payments.Services.KountDisputeService;
using FlexCharge.Payments.Services.Nuvei;
using FlexCharge.Payments.Services.PaymentServices.Gateways;
using FlexCharge.Payments.Services.PaymentServices.Gateways.Adyen;
using FlexCharge.Payments.Services.PaymentServices.Gateways.Checkout;
using FlexCharge.Payments.Services.PaymentServices.Gateways.Nmi;
using FlexCharge.Payments.Services.PaymentServices.Gateways.Paysafe;
using FlexCharge.Payments.Services.PaymentServices.Gateways.Revolv;
using FlexCharge.Payments.Services.PaymentServices.Gateways.Stripe;
using FlexCharge.Payments.Services.PaymentServices.Providers.Adyen;
using FlexCharge.Payments.Services.PaymentServices.Providers.Checkout;
using FlexCharge.Payments.Services.PaymentServices.Providers.Dummy;
using FlexCharge.Payments.Services.PaymentServices.Providers.Epx;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe;
using FlexCharge.Payments.Services.PaymentServices.Providers.Rapyd;
using FlexCharge.Payments.Services.PaymentServices.Providers.Revolv3;
using FlexCharge.Payments.Services.PaymentServices.Providers.Svb;
using FlexCharge.Payments.Services.Stripe;
using FlexCharge.Payments.Services.TransactionMonitoring.MerchantTransactions;
using FlexCharge.Payments.Services.TransactionMonitoring.PartnerTransactions;


namespace FlexCharge.Payments
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddReporting();
            services.AddTelemetry();
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();

            services.AddActivities();

            services.AddHttpClient();

            // Attention!!! Do not add the same service as transient below
            services.AddHttpClient<PaysafeSDK>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5));

            // Attention!!! Do not add the same service as transient below
            services.AddHttpClient<RapydSdk>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5));

            // Attention!!! Do not add the same service as transient below
            services.AddHttpClient<EpxSdk>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5));

            // Attention!!! Do not add the same service as transient below
            services.AddHttpClient<Revolv3Sdk>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5));
            // .AddTransientHttpErrorPolicy(policyBuilder =>
            //     policyBuilder.WaitAndRetryAsync(
            //         3, retryNumber => TimeSpan.FromMilliseconds(600)));

            // // Attention!!! Do not add the same service as transient below
            // services.AddHttpClient<NmiPaymentSdk>()
            //     .SetHandlerLifetime(TimeSpan.FromMinutes(5));

            services.Configure<EpxOptions>(Configuration.GetSection(EpxOptions.ConfigName));

            services.AddScoped<AdyenWebhookService>();
            services.AddScoped<Adyen.Util.HmacValidator>();

            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient<IPaymentInstrumentsService, PaymentInstrumentsService>();
            services.AddTransient<ITransactionService, TransactionService>();
            services.AddTransient<IMerchantServices, MerchantServices>();
            services.AddTransient<IDisputeServices, DisputeServices>();
            services.AddTransient<IDisputeImportServices, DisputeImportService>();
            services.AddTransient<IIncomingWebhooksService, IncomingWebhooksService>();
            services.AddTransient<IFinancialAccountsServices, FinancialAccountsServices>();
            services.AddTransient<IGeoServices, GeoServices>();


            services.AddTransient<IAlertProvidersService, AlertProvidersService>();

            services.AddTransient<IMyRcvrAlertService, MyRcvrAlertService>();

            services.AddTransient<MyRcvrChargebackReportService>();
            services.AddTransient<MyRcvrRdrReportService>();
            services.AddTransient<DisputeQueueService>();

            services.AddTransient<IKountAlertService, KountAlertService>();
            services.AddTransient<IWebhookService, KountWebhooksService>();

            services.AddTransient<IWebhookService, PayarcWebhookService>();
            services.AddTransient<IWebhookService, ChargeblastWebhooksService>();
            services.AddTransient<IWebhookService, MyRcvrWebhooksService>();

            services.AddTransient<KountReportService>();

            services.AddTransient<ServiceCollectionExtensions.WebhookAlertProviderResolver>(provider => key =>
            {
                var providers = provider.GetServices<IWebhookService>();
                switch (key)
                {
                    case WebhookProviders.Chargeblast:
                        return providers.First(x => x.GetType() == typeof(ChargeblastWebhooksService));
                    case WebhookProviders.Kount:
                        return providers.First(x => x.GetType() == typeof(KountWebhooksService));
                    case WebhookProviders.Payarc:
                        return providers.First(x => x.GetType() == typeof(PayarcWebhookService));
                    case WebhookProviders.MyRCVR:
                        return providers.First(x => x.GetType() == typeof(MyRcvrWebhooksService));
                    default:
                        throw new KeyNotFoundException(key);
                }
            });

            services.AddTransient<ChargeblastSDK>();
            services.AddTransient<IDisputeAlertsService, ChargeblastService>();
            services.AddTransient<ServiceCollectionExtensions.DisputeAlertProviderResolver>(provider => key =>
            {
                var providers = provider.GetServices<IDisputeAlertsService>();
                switch (key)
                {
                    case AlertProviders.Chargeblast:
                        return providers.First(x => x.GetType() == typeof(ChargeblastService));
                    default:
                        throw new KeyNotFoundException(key);
                }
            });

            services.AddTransient<DefaultReportService>();
            services.AddTransient<EmsReportService>();
            services.AddTransient<MerlinkReportService>();
            services.AddTransient<PaysafeAmrFraudReportService>();
            services.AddTransient<PaysafeReportService>();
            services.AddTransient<PaysafeOldReportService>();
            services.AddTransient<CheckoutReportService>();
            services.AddTransient<PayarcReportService>();
            services.AddTransient<DisputiferReportService>();
            services.AddTransient<EurekapaymentsReportService>();
            services.AddTransient<LuqraReportService>();
            services.AddTransient<MaverickReportService>();
            services.AddTransient<PaycosmoReportService>();
            services.AddTransient<QuantumReportService>();
            services.AddTransient<StaxReportService>();
            services.AddTransient<FiservReportService>();
            services.AddTransient<StripeReportService>();
            services.AddTransient<NuveiReportService>();
            services.AddTransient<ReportServiceFactory>();
            services.AddTransient<VerifiReportService>();

            services.AddTransient<IReportMatchService, ReportMatchService>();

            services.AddTransient<INotificationReportService, NotificationReportService>();

            services.AddTransient<IJwtHandler, BasicOauthJwtHandler>();

            services.AddAWSService<IAmazonS3>();
            services.AddTransient<ICloudStorage, AmazonS3Storage>();
            services.AddTransient<ISftpClientService, SftpClientService>();
            services.AddSingleton<IAmazonSecretsManager, AmazonSecretsManagerClient>();

            services.AddTransient<IReportsService, ReportsService>();
            services.Configure<SftpDisputesOptions>(Configuration.GetSection("disputesSftp"));

            services.AddTransient<ILinkedPaymentInstrumentsService, LinkedPaymentInstrumentsService>();

            services.AddTransient<IStripeBillingPortalService, StripeBillingPortalService>();

            services.AddScoped<NmiGatewayService>();
            services.AddScoped<CheckoutGatewayService>();
            services.AddScoped<PaysafeGatewayService>();
            services.AddScoped<RapydGatewayService>();
            services.AddScoped<Revolv3GatewayService>();
            services.AddScoped<AdyenGatewayService>();
            services.AddScoped<StripeGatewayService>();

            services.AddAmazonSecretsManager();

            services.AddHateoasLinksSupport();
            // services.AddTransient<IBatchService, BatchService>();
            // services.AddTransient<IPayoutService, PayoutService>();

            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));

            //services.Configure<StripeOptions>(Configuration.GetSection(StripeOptions.SectionName));

            services.Configure<SpreedlyOptions>(Configuration.GetSection(SpreedlyOptions.SectionKey));
            services.Configure<Spreedly3DSMerchantOptions>(
                Configuration.GetSection(Spreedly3DSMerchantOptions.SectionKey));
            services.AddTransient<ISpreedlyService, SpreedlyService>();

            services.Configure<TokenExOptions>(Configuration.GetSection(TokenExOptions.SectionKey));
            services.AddTransient<ITokenExService, TokenExService>();

            services.AddTransient<NmiPaymentSdk, NmiPaymentSdk>();

            services.AddSingleton<ICardBrandDetector, CardBrandDetector>();
            // CheckoutServiceCollection.AddCheckoutSdk(services, Configuration);
            services.Configure<AppOptions>(Configuration.GetSection(AppOptions.SectionKey));

            services.AddTransient<ISVBService, SvbService>();
            services.AddTransient<INuveiRequestExecutor, NuveiExecutorExtended>();

            services.AddScoped<SupportedGatewayRegistry>();

            services.AddTransient<StripeSubscriptionSdk>();

            services.AddScoped<MetricsService>();

            services.AddTransient<IPaymentOrchestrator, PaymentOrchestrator>();
            services.AddTransient<IPartnerPaymentServices, PartnerPaymentServices>();
            services.AddTransient<IPaymentProvider, NuveiPaymentProvider>();
            services.AddTransient<IPaymentProvider, SpreedlyPaymentProvider>();
            services.AddTransient<IPaymentProvider, SVBPaymentProvider>();
            services.AddTransient<IPaymentProvider, StripePaymentProvider>();
            services.AddTransient<IPaymentProvider, NmiPaymentProvider>();
            services.AddTransient<IPaymentProvider, DummyPaymentProvider>();
            services.AddTransient<IPaymentProvider, CheckoutPaymentProvider>();
            services.AddTransient<IPaymentProvider, PaysafePaymentProvider>();
            services.AddTransient<IPaymentProvider, RapydPaymentProvider>();
            services.AddTransient<IPaymentProvider, EpxPaymentProvider>();
            services.AddTransient<IPaymentProvider, RevolvPaymentProvider>();

            services.AddTransient<ServiceCollectionExtensions.PaymentProviderResolver>(provider => key =>
            {
                var providers = provider.GetServices<IPaymentProvider>();
                switch (key)
                {
                    case GatewayTypesConstants.Nuvei:
                        return providers.First(x => x.GetType() == typeof(NuveiPaymentProvider));
                    case GatewayTypesConstants.Svb:
                        return providers.First(x => x.GetType() == typeof(SVBPaymentProvider));
                    case GatewayTypesConstants.Spreedly_Nmi:
                    case GatewayTypesConstants.SafeCharge:
                    case GatewayTypesConstants.Spreedly_Stripe_PI:
                        return providers.First(x => x.GetType() == typeof(SpreedlyPaymentProvider));
                    case GatewayTypesConstants.Stripe:
                        return providers.First(x => x.GetType() == typeof(StripePaymentProvider));
                    case GatewayTypesConstants.Nmi_v2:
                        return providers.First(x => x.GetType() == typeof(NmiPaymentProvider));
                    case GatewayTypesConstants.FlexChargeDummy:
                        return providers.First(x => x.GetType() == typeof(DummyPaymentProvider));
                    case GatewayTypesConstants.Checkout:
                        return providers.First(x => x.GetType() == typeof(CheckoutPaymentProvider));
                    case GatewayTypesConstants.FlexChargeDummy2:
                        return providers.First(x => x.GetType() == typeof(DummyPaymentProvider));
                    case GatewayTypesConstants.Epx:
                        return providers.First(x => x.GetType() == typeof(EpxPaymentProvider));
                    case GatewayTypesConstants.Paysafe:
                        return providers.First(x => x.GetType() == typeof(PaysafePaymentProvider));
                    case GatewayTypesConstants.Rapyd:
                        return providers.First(x => x.GetType() == typeof(RapydPaymentProvider));
                    case GatewayTypesConstants.Revolv3:
                        return providers.First(x => x.GetType() == typeof(RevolvPaymentProvider));
                    case GatewayTypesConstants.Adyen:
                        return providers.First(x => x.GetType() == typeof(AdyenPaymentProvider));
                    default:
                        throw new KeyNotFoundException(key);
                }
            });

            #region Initializing Payment Providers Response Code Mappers

            Services.PaymentServices.Providers.Checkout.CheckoutResponseMapper.Initialize();
            Services.PaymentServices.Providers.Epx.EpxResponseMapper.Initialize();
            Services.PaymentServices.Providers.Nuvei.NuveiResponseMapper.Initialize();
            //Services.PaymentServices.Providers.Spreedly.DummyResponseMapper.Initialize();
            //Services.PaymentServices.Providers.Svb.DummyResponseMapper.Initialize();

            #endregion

            //Open banking services
            services.Configure<PlaidOptions>(Configuration.GetSection(PlaidOptions.SectionKey));
            services.Configure<PlaidCredentials>(Configuration.GetSection(PlaidOptions.SectionKey));
            services.AddSingleton<PlaidClient>();
            services.AddTransient<IOpenBankingService, OpenBankingService>();

            services.AddScoped<VgsSdk>();
            services.AddScoped<IRealTimeAccountUpdater, VgsAccountUpdaterService>();


            services.AddTransient<ITransactionMonitoringService, TransactionMonitoringService>();
            services.AddTransient<ITransactionProcessingService, TransactionProcessingService>();

            services.AddTransient<IPartnerTransactionMonitoringService, PartnerTransactionMonitoringService>();
            services.AddTransient<IPartnerTransactionProcessingService, PartnerTransactionProcessingService>();

            services.AddOptions();

            var connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
            connectionString =
                "Host=localhost;port=5432;Database=fc.payments;Username=payments-service-staging;Password=*****";
#endif

            services
                .AddNpgsqlDbContext<DbContext, PostgreSQLDbContext>(connectionString, npgsqlOptionsAction: options =>
                    options
                        .MapEnum<PaymentMethodType>()
                        .MapEnum<TransactionStatus>()
                );
            //.AddNpgsqlDbContext<PostgreSQLDbContext>("Host=localhost;port=5432;Database=fc.payments;Username=payments-service-staging;Password=*****");


            services.AddJwt();
            services.AddAuthorization(options =>
                {
                    options.AddPolicy(MyPolicies.SUPER_ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN));
                    options.AddPolicy(MyPolicies.ADMINS_ONLY,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.MERCHANT_ADMIN));
                    options.AddPolicy(MyPolicies.USERS,
                        policy => policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.USER));
                    options.AddPolicy(MyPolicies.ADMINS_AND_MERACHANTS_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP, SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS_AND_MERCHANT_ADMINS_AND_MERCHANT_DEVELOPER,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.MERCHANT_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });
                    options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });

                    options.AddPolicy(PaymentsScopes.PAYMENTS_READ,
                        policy => policy.RequireAssertion(context =>
                        {
                            var scopeClaim = context.User.FindAll(c => c.Type == "scope" || c.Type == "scopes");
                            return scopeClaim.Any(x => x.Value.Contains(PaymentsScopes.PAYMENTS_READ));
                        }));
                    options.AddPolicy(PaymentsScopes.PAYMENTS_WRITE,
                        policy => policy.RequireAssertion(context =>
                        {
                            var scopeClaim = context.User.FindAll(c => c.Type == "scope" || c.Type == "scopes");
                            return scopeClaim.Any(x =>
                                x.Value.Contains(PaymentsScopes.PAYMENTS_WRITE));
                        }));

                    options.AddPolicy(MyPolicies.ADMINS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN);
                        });

                    options.AddPolicy(MyPolicies.ADMINS_AND_ALL_MERCHANTS_AND_PARTNER_ADMINS,
                        policy =>
                        {
                            policy.RequireClaim(MyClaimTypes.COGNITO_GROUP,
                                SuperAdminGroups.SUPER_ADMIN,
                                SuperAdminGroups.PARTNER_ADMIN,
                                SuperAdminGroups.INTEGRATION_PARTNER_ADMIN,
                                MerchantGroups.MERCHANT_ADMIN,
                                MerchantGroups.MERCHANT_SUPPORT,
                                MerchantGroups.MERCHANT_SUPPORT_ADMIN,
                                MerchantGroups.MERCHANT_FINANCE,
                                MerchantGroups.MERCHANT_DEVELOPER);
                        });
                }
            );

            services.AddMassTransit<Startup>(x =>
            {
                x.AddFastRequestClient<TokenizeInstrumentCommand>(Microservices.Vault);
                x.AddFastRequestClient<GetByVaultIdCommand>(Microservices.Vault);
                x.AddFastRequestClient<DeTokenizeInstrumentCommand>(Microservices.Vault);
                x.AddFastRequestClient<RecacheVerificationValueCommand>(Microservices.Vault);
                x.AddFastRequestClient<UpdateCardCommand>(Microservices.Vault);
            });

            #region GRPC

            services.AddFlexGrpc();

            // services.AddFlexGrpcClient<Grpc.Vault.GrpcVaultService.GrpcVaultServiceClient, VaultGrpcOptions>(
            //     nameof(VaultGrpcOptions.VaultEndpoint));

            #endregion

            services.AddRedisCache();
            services.AddAutoMapper(typeof(Startup));

            services.AddControllers()
                .AddNewtonsoftJson(x =>
                    x.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore);


            //Added to fix circular reference errors (somehow configuration above was not enough)
            JsonConvert.DefaultSettings = () => new JsonSerializerSettings()
                {ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore};

            services.AddSwaggerDocs();

            services.AddRedisCache();
            services.AddBigPayloadSupport();
            services.AddRedLockDistributedLock();

            // services.AddDynamoDBAsync(tables => tables
            //     .CreateTableIfMissingAsync<ProvidersMetricsTable>());

            services.AddEmailClient();

            services.AddBackgroundWorkerService(Configuration);

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });


            // Validate data maps (provider response code mappings, etc.)
            DataMapsValidator.ValidateDataMapsAndThrow<Startup>();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            app.UseCors("CorsPolicy");

            //app.UseHttpsRedirection();
            app.UseSwaggerDocs();

            app.UseAutoMigrations<PostgreSQLDbContext>();

            app.UseRouting();
            app.UseAuthorization();
            app.UseCorrelationId();
            // app.UseFlexGrpc<PaymentsGrpcServer>();

            app.UsePublicHttpRequestsTelemetry(); // Should be right before UseEndpoints to work correctly
            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            #region GRPC

            app.UseGrpcEndpoints(endpoints =>
                endpoints
                    .Map<GrpcGreeterService>()
                    .Map<GrpcPaymentsService>()
            );

            #endregion

            app.UseMassTransit();
        }
    }
}