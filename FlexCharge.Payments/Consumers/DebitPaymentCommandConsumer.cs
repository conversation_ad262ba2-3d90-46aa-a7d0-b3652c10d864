using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.MassTransit;
using FlexCharge.Contracts.Commands;
using FlexCharge.Payments.Domain.Payments.PaymentInstruments;
using FlexCharge.Payments.Services.PaymentInstrumentsServices;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using Microsoft.Extensions.DependencyInjection;
using BillingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.BillingAddress;
using ShippingAddress = FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels.ShippingAddress;

namespace FlexCharge.Payments.Consumers;

public class DebitPaymentCommandConsumer :
    IdempotentCommandConsumer<DebitPaymentCommand, DebitPaymentCommandResponse>
{
    private readonly IPaymentInstrumentsService _instruments;
    private readonly IPaymentOrchestrator _paymentOrchestrator;

    public DebitPaymentCommandConsumer(
        IPaymentInstrumentsService instruments,
        IPaymentOrchestrator paymentOrchestrator,
        IServiceScopeFactory serviceScopeFactory) : base(serviceScopeFactory)
    {
        _instruments = instruments;
        _paymentOrchestrator = paymentOrchestrator;
    }

    protected override async Task<DebitPaymentCommandResponse> ConsumeCommand(DebitPaymentCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            Workspan
                .Baggage("Mid", command.Mid)
                .Baggage("OrderId", command.OrderId)
                .LogEnterAndExit();

            var descriptor = new DescriptorDTO();
            if (command.UseDynamicDescriptor)
            {
                descriptor.Name = command.Descriptor.Name;
                descriptor.Phone = command.Descriptor.Phone;
                descriptor.Address = command.Descriptor.Address;
                descriptor.City = command.Descriptor.City;
                descriptor.State = command.Descriptor.State;
                descriptor.Postal = command.Descriptor.Postal;
                descriptor.Country = command.Descriptor.Country;
                descriptor.Mcc = command.Descriptor.Mcc;
                descriptor.Url = command.Descriptor.Url;
                descriptor.MerchantId = command.Descriptor.MerchantId;
            }

            var billingAddress = command.BillingAddress;
            var shippingAddress = command.ShippingAddress;

            if (command.UseBillingAsShipping)
            {
                shippingAddress = new Contracts.Common.ShippingAddress
                {
                    FirstName = billingAddress.FirstName,
                    LastName = billingAddress.LastName,
                    Phone = billingAddress.Phone,
                    Address1 = billingAddress.Address1,
                    Address2 = billingAddress.Address2,
                    City = billingAddress.City,
                    State = billingAddress.State,
                    Zip = billingAddress.Zip,
                    Country = billingAddress.Country
                };
            }

            var saleRequest = new SaleRequest()
            {
                Token = command.PaymentInstrument.PaymentInstrumentToken,
                CardType = CardTypeHelper.ConvertFromString(command.PaymentInstrument.Type),
                //Deprecated (use ThreeDS property instead)
                //ScaAuthenticationToken = gateway.Name == "nmi" ? command.ScaAuthenticationToken : null,
                Mid = command.Mid,
                CurrencyCode = command.Currency,
                Amount = command.Amount,
                Discount = command.Discount,
                UseDynamicDescriptor = command.UseDynamicDescriptor,
                PayerId = command.PayerId,
                OrderId = command.OrderId,
                DuplicateCheckInSeconds =
                    command
                        .OverrideDuplicateOrderCheckTimespan, // see: https://docs.qpilot.cloud/docs/2000-nmi-error-code-300-duplicate-transaction-refid__
                BillingAddress = new BillingAddress
                {
                    FirstName = billingAddress.FirstName,
                    LastName = billingAddress.LastName,
                    Address1 = billingAddress.Address1,
                    Address2 = billingAddress.Address2,
                    City = billingAddress.City,
                    State = billingAddress.State,
                    Zip = billingAddress.Zip,
                    Country = billingAddress.Country,
                    PhoneNumber = billingAddress.Phone,
                    Email = command.PayerEmail
                },
                ShippingAddress = new ShippingAddress
                {
                    FirstName = shippingAddress?.FirstName,
                    LastName = shippingAddress?.LastName,
                    Address1 = shippingAddress?.Address1,
                    Address2 = shippingAddress?.Address2,
                    City = shippingAddress?.City,
                    State = shippingAddress?.State,
                    Zip = shippingAddress?.Zip,
                    Country = shippingAddress?.Country,
                    PhoneNumber = shippingAddress?.Phone,
                },
                Descriptor = descriptor,
                Device = new DeviceDetailsDTO
                {
                    IpAddress = command.DeviceInformation?.IpAddress,
                    UserAgent = command.DeviceInformation?.UserAgent
                },
                Modifiers = command.Modifiers == null
                    ? null
                    : PaymentModifiers.FromPaymentModifiersModel(command.Modifiers),
                IsCit = command.IsCit,
                NetworkReferenceData = command.SchemeTransactionId != null
                    ? new NetworkReferenceData
                    {
                        TransactionId = command.SchemeTransactionId,
                    }
                    : null,
                TryUseAccountUpdater = command.TryUseAccountUpdater,
                UserDefinedFields = command.UserDefinedFields
            };

            saleRequest.BillingAddress.Email = command.PayerEmail;

            if (command.ThreeDs != null)
            {
                saleRequest.ThreeDS = new ThreeDsecureDTO
                {
                    EcommerceIndicator = command.ThreeDs.EcommerceIndicator,
                    AuthenticationValue = command.ThreeDs.AuthenticationValue,
                    DirectoryServerTransactionId = command.ThreeDs.DirectoryServerTransactionId,
                    ThreeDsVersion = command.ThreeDs.ThreeDsVersion,
                    Xid = command.ThreeDs.Xid,
                    AuthenticationValueAlgorithm = command.ThreeDs.AuthenticationValueAlgorithm,
                    DirectoryResponseStatus = command.ThreeDs.DirectoryResponseStatus,
                    AuthenticationResponseStatus = command.ThreeDs.AuthenticationResponseStatus,
                    Enrolled = command.ThreeDs.Enrolled == true ? "Y" : "N",
                };
            }

            var saleResponse =
                await _paymentOrchestrator.SaleAsync(saleRequest,
                    new OrchestrationOptions
                    {
                        IsCascadingPayment = command.PerformCascadingPayment,
                        Order = command.GatewayOrder,
                        PreviousOrder = command.GatewayOrder,
                        IsCIT = command.IsCit,
                        Currency = command.Currency,
                        Country = command.PaymentInstrument?.Country,
                        AlreadyTriedProviders = command.AlreadyTriedProviders?.Select(x =>
                            (SupportedGatewayId: x.SupportedGatewayId, Timestamp: x.Timestamp)).ToList(),
                        RiskTier = command.RiskTier
                    },
                    token: CancellationToken.None);

            var gatewayNotFound = saleResponse.ErrorsWithCodes.Any(x => x.Key == "GatewayNotFound");

            return new DebitPaymentCommandResponse
            {
                Success = saleResponse.Success,
                ResponseCode = saleResponse.ProviderResponseCode,
                ResponseMessage = saleResponse.ProviderResponseMessage,
                BinNumber = saleResponse.BinNumber,
                TransactionId = saleResponse.TransactionId,

                GatewayFound = !gatewayNotFound,
                GatewayId = saleResponse.ProviderId,
                SupportedGatewayId = saleResponse.SupportedGatewayId,
                GatewayOrder = saleResponse.GatewayOrder,

                Provider = saleResponse.Provider,
                ProviderResponseCode = saleResponse.ProviderResponseCode,
                ProviderTransactionToken = saleResponse.ProviderTransactionToken,

                InternalResponseCode = saleResponse.InternalResponseCode,
                InternalResponseMessage = saleResponse.InternalResponseMessage,
                InternalResponseGroup = saleResponse.InternalResponseGroup,

                CvvCode = saleResponse.CvvCode,
                AvsCode = saleResponse.AvsCode,
                NextGateway = saleResponse?.NextGatewayOrder
            };
        }
        catch (Exception e)
        {
            Workspan.RecordFatalException(e);
            throw;
        }
    }
}