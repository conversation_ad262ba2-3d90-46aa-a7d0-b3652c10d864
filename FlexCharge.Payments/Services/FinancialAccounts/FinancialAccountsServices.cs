using FlexCharge.Common;
using FlexCharge.Payments.DTO;
using FlexCharge.Payments.Entities;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.FinancialAccounts;
using FlexCharge.Payments.Entities.Extensions;
using FlexCharge.Payments.Services.FinancialAccounts.Models;
using FlexCharge.Payments.Services.PaymentServices;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.SVBAchServices.Models;
using FlexCharge.Utils;
using MassTransit;
using Microsoft.EntityFrameworkCore;

namespace FlexCharge.Payments.Services.FinancialAccounts;

public class FinancialAccountsServices : IFinancialAccountsServices
{
    private readonly PostgreSQLDbContext _dbContext;
    private readonly IPublishEndpoint _publisher;
    private readonly IPartnerPaymentServices _partnerPaymentServices;


    public FinancialAccountsServices(PostgreSQLDbContext dbContext, IPublishEndpoint publisher,
        IPartnerPaymentServices partnerPaymentServices)
    {
        _dbContext = dbContext;
        _publisher = publisher;
        _partnerPaymentServices = partnerPaymentServices;
    }

    public async Task<FinancialAccountVerificationResult> Verify(FinancialAccountVerificationRequest request,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<FinancialAccountsServices>()
            .Baggage(nameof(request.Id), request.Id)
            .LogEnterAndExit();

        var result = new FinancialAccountVerificationResult();
        try
        {
            var record =
                await _dbContext.FinancialAccounts.SingleOrDefaultAsync(
                    x => x.Id == request.Id, cancellationToken: token);

            if (record == null)
                throw new FlexNotFoundException("Cannot verify Financial account, not found");

            if (record.VerificationState == FinancialAccountState.Verified.ToString())
                throw new FlexValidationException("Financial account already verified");

            #region Get MasterAccount for collection

            var flexCollectionAccount = await _dbContext.FinancialAccounts
                .FirstOrDefaultAsync(x =>
                        x.RelatedEntityType == FinancialAccountsRelatedEntityType.MasterAccount &&
                        x.Type == FinancialAccountsTypes.BankAccountAch,
                    token);

            if (flexCollectionAccount == null)
                throw new FlexChargeException("ERROR: Can't find flex financial account");

            #endregion

            var verificationResult = await _partnerPaymentServices.VerifyAchAccountAsync(
                new AchAccountVerificationRequest
                {
                    Pid = record.RelatedEntityId,
                    ProviderNameIdentifier = "svb",
                    Amount = 0,
                    Currency = record.Currency,
                    CurrencyCode = record.Currency,
                    IsVerifiedAch = false,
                    IdentificationNumber = UniqueIdsHelper.GeneratePseudoUniqueId(record.Id),
                    Processor = GatewayTypesConstants.Svb,
                    Provider = "svb",
                    CompanyEntryDescription = "FLX ACH",
                    EffectiveEntryDate = DateTime.Now.Date.ToString("yyyy-MM-dd"),
                    SecType = SecCodeEnum.PPD,
                    Sender = record.ToAchAccountDTO(),
                    Receiver = flexCollectionAccount.ToAchAccountDTO()
                }, token);

            record.VerificationState = verificationResult.Success
                ? FinancialAccountState.PendingVerification.ToString()
                : FinancialAccountState.FailedVerification.ToString();

            record.VerificationTransactionId = verificationResult.TransactionId;

            _dbContext.FinancialAccounts.Update(record);

            var saved = await _dbContext.SaveChangesAsync(token);
            if (saved <= 0)
                throw new FlexChargeException("Cannot store Financial account DB error");

            result.State = record.VerificationState;


            return result;
        }
        catch (FlexNotFoundException e)
        {
            workspan.Log.Error(e, e.Message);

            await _publisher.Publish(new FinancialAccountVerificationFailedEvent()
            {
                Message = e.Message,
                FinancialAccountId = request.Id
            });
            throw;
        }
        catch (FlexValidationException e)
        {
            workspan.Log.Error(e, e.Message);
            throw;
        }
        catch (FlexChargeException e)
        {
            workspan.Log.Error(e, e.Message);

            await _publisher.Publish(new FinancialAccountVerificationFailedEvent()
            {
                Message = e.Message,
                FinancialAccountId = request.Id
            });
            throw;
        }
    }

    public async Task<Guid> CreateAsync(FinancialAccount financialAccount, bool updateIfExists = false)
    {
        using var workspan = Workspan.Start<FinancialAccountsServices>()
            .Baggage(nameof(financialAccount.RelatedEntityType), financialAccount.RelatedEntityId)
            .LogEnterAndExit();

        try
        {
            var existing =
                await _dbContext.FinancialAccounts.SingleOrDefaultAsync(
                    x =>
                        (x.RelatedEntityId == financialAccount.RelatedEntityId &&
                         x.RelatedEntityType == financialAccount.RelatedEntityType) ||
                        x.Id == financialAccount.Id);

            if (existing != null)
            {
                workspan.Log.Information(
                    "Financial account already exists for {RelatedEntityType} {RelatedEntityId} updateIfExists: {UpdateIfExists}",
                    financialAccount.RelatedEntityType, financialAccount.RelatedEntityId, updateIfExists);

                if (updateIfExists)
                    await UpdateAsync(financialAccount);

                return existing.Id;
            }

            await _dbContext.FinancialAccounts.AddAsync(financialAccount);
            var saved = await _dbContext.SaveChangesAsync();

            if (saved <= 0) throw new FlexChargeException("Cannot create Financial account DB error");

            await _publisher.Publish(new FinancialAccountCreatedEvent
            {
                Id = financialAccount.Id,
                Name = financialAccount.AccountName,
                Description = financialAccount.AccountDescription,
                RelatedEntityId = financialAccount.RelatedEntityId,
                RelatedEntityType = financialAccount.RelatedEntityType,
                AccountType = financialAccount.AccountType,
                Currency = financialAccount.Currency
            });

            return financialAccount.Id;
        }
        catch (FlexChargeException e)
        {
            workspan.Log.Error(e, "Failed to create financial account");

            await _publisher.Publish(new FinancialAccountCreateFailedEvent
            {
                Error = e.Message,
                RelatedEntityId = financialAccount.RelatedEntityId,
                RelatedEntityType = financialAccount.RelatedEntityType
            });
            throw;
        }
    }

    public async Task<Guid> UpdateAsync(FinancialAccount financialAccount, bool createIfNotExists = false)
    {
        using var workspan = Workspan.Start<FinancialAccountsServices>()
            .Baggage(nameof(financialAccount.Id), financialAccount.Id)
            .Baggage(nameof(financialAccount.RelatedEntityType), financialAccount.RelatedEntityId)
            .LogEnterAndExit();

        try
        {
            //get the existing one
            var existing =
                await _dbContext.FinancialAccounts.SingleOrDefaultAsync(
                    x =>
                        (x.RelatedEntityId == financialAccount.RelatedEntityId &&
                         x.RelatedEntityType == financialAccount.RelatedEntityType) ||
                        x.Id == financialAccount.Id);

            if (existing == null)
            {
                if (createIfNotExists)
                {
                    return await CreateAsync(financialAccount);
                }

                throw new FlexNotFoundException($"Cannot update Financial account, not found");
            }

            //if existing account/reoutung number changed drop the verification
            if (existing.AccountNumber != financialAccount.AccountNumber ||
                existing.RoutingNumber != financialAccount.RoutingNumber)
            {
                existing.VerificationState = FinancialAccountState.VerificationRequired.ToString();
                existing.VerificationTransactionId = Guid.Empty;
            }

            existing.AccountDescription = financialAccount.AccountDescription;
            existing.AccountName = financialAccount.AccountName;
            existing.AccountType = financialAccount.AccountType;
            existing.AccountNumber = financialAccount.AccountNumber;
            existing.BankName = financialAccount.BankName;
            existing.RoutingNumber = financialAccount.RoutingNumber;
            existing.Currency = financialAccount.Currency;

            _dbContext.FinancialAccounts.Update(existing);
            var saved = await _dbContext.SaveChangesAsync();

            if (saved <= 0) throw new FlexChargeException("Cannot update Financial account DB error");

            await _publisher.Publish(new FinancialAccountUpdatedEvent
            {
                Id = existing.Id,
                RelatedEntityId = existing.RelatedEntityId,
                RelatedEntityType = existing.RelatedEntityType,
                RelatedEntityDba = existing.AccountName,
                Name = existing.AccountName,
                Description = existing.AccountDescription,
                Currency = existing.Currency
            });

            return existing.Id;
        }
        catch (FlexChargeException e)
        {
            workspan.Log.Error(e, "Failed to update financial account");

            await _publisher.Publish(new FinancialAccountUpdateFailedEvent()
            {
                Error = e.Message
            });
            throw;
        }
    }

    public async Task<IPagedList<FinancialAccountDTO>> GetFinancialAccountsAsync(FinancialAccountQueryDTO payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<FinancialAccountsServices>()
            .LogEnterAndExit();

        try
        {
            var query = _dbContext.FinancialAccounts.AsQueryable();

            query = query.OrderByDescending(x => x.CreatedOn);

            if (payload.RelatedEntityType != null)
            {
                query = query.Where(x => x.RelatedEntityType == payload.RelatedEntityType);
            }

            var result = await query
                .Select(x => new FinancialAccountDTO
                {
                    Id = x.Id,
                    VerificationState = x.VerificationState,
                    RelatedEntityId = x.RelatedEntityId,
                    RelatedEntityType = x.RelatedEntityType,
                    Currency = x.Currency,
                    AccountName = x.AccountName,
                    BankName = x.BankName,
                    AccountType = x.AccountType,
                    AccountNumber = x.AccountNumber,
                    RoutingNumber = x.RoutingNumber,
                    AccountDescription = x.AccountDescription,
                    MaskedAccountNumber = x.AccountNumber.Length >= 4
                        ? x.AccountNumber.Mask(0, x.AccountNumber.Length - 4, 'x')
                        : x.AccountNumber,
                    MaskedRoutingNumber = x.RoutingNumber.Length >= 4
                        ? x.RoutingNumber.Mask(0, x.RoutingNumber.Length - 4, 'x')
                        : x.RoutingNumber,
                })
                .ToPagedListAsync(payload.PageNumber, payload.PageSize);

            return result;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "Failed to get financial accounts");
            throw;
        }
    }
}