using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Utils;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.RuntimeEnvironment;
using FlexCharge.Common.Shared.Payments;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.CardBrands;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Services.PaymentServices.Interfaces;
using FlexCharge.Payments.Services.PaymentServices.Models;
using FlexCharge.Payments.Services.PaymentServices.PaymentServiceModels;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Authentication;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Card;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.CompleteAuthorization;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.Response;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Refund;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Settlement;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Settlement.CancelSettlement;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Verification;
using FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Void;
using FlexCharge.PaymentsUtils;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using AvsResponse = FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe.Authorization.AvsResponse;
using IResult = FlexCharge.Payments.Services.PaymentServices.Interfaces.IResult;
using Transaction = FlexCharge.Payments.Entities.Transaction;
using TransactionStatus = FlexCharge.Payments.Entities.TransactionStatus;

namespace FlexCharge.Payments.Services.PaymentServices.Providers.Paysafe;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using JsonConverter = Newtonsoft.Json.JsonConverter;

public class PaysafePaymentProvider : PaymentProviderBase
{
    private readonly PostgreSQLDbContext _context;

    private PaysafeSDK _sdk;


    private JsonSerializerOptions _jsonOptions;

    private JsonSerializerSettings _jsonSerializeSettings = new()
    {
        ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore,
        ContractResolver = new CamelCasePropertyNamesContractResolver(),
        Converters = new List<JsonConverter>() {new StringEnumConverter()}
    };

    public PaysafePaymentProvider(
        IMapper mapper,
        PostgreSQLDbContext context,
        PaysafeSDK sdk)
    {
        _context = context;
        _sdk = sdk;
        _jsonOptions = new()
        {
            ReferenceHandler = ReferenceHandler.IgnoreCycles,
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
        _jsonOptions.Converters.Add(new JsonStringEnumConverter());
    }

    private async Task<(string urlPrefix, string username, string password)> GetCredentialsAsync(bool isSandbox,
        Guid supportedGatewayId, CancellationToken token = default)
    {
        var supportedGateway = await _context.SupportedGateways
            .Where(x => x.Id == supportedGatewayId && x.Sandbox == isSandbox)
            .FirstOrDefaultAsync(token);

        if (supportedGateway == null)
            throw new Exception("Paysafe supported gateway not found");

        return _sdk.GetCredentials(supportedGateway, isSandbox);
    }

    private void LogRequest<TReq>(TReq request, Workspan workspan, Action<TReq> pciCleaning = null,
        [CallerMemberName] string callername = "")
        where TReq : IMerchantRequest
    {
        if (request is not null)
        {
            try
            {
                var logreq = JsonConvert.DeserializeObject(
                    JsonConvert.SerializeObject(request, _jsonSerializeSettings), request.GetType(),
                    _jsonSerializeSettings);

                if (pciCleaning is not null)
                {
                    pciCleaning((TReq) logreq);
                }

                workspan.Log.Information("Paysafe {Request} for {Method}= ",
                    JsonConvert.SerializeObject(logreq, _jsonSerializeSettings), callername);
            }
            catch (Exception e)
            {
                workspan.Log.Error("Paysafe request log exception {Message}", e.Message);
            }
        }
    }


    private void UpdateResult(IPaysafeResponse response, Interfaces.IResult result, string orderId, Workspan workspan)
    {
        if (response.Error is not null)
        {
            result.ProviderResponseCode = response.Error.Code;
            result.ProviderResponseMessage = response.Error.Message;

            result.AddErrorWithCode(response.Error.Code, response.Error.Message);
            if (response.Error.FieldErrors is not null)
            {
                foreach (var fieldError in response.Error.FieldErrors)
                {
                    result.AddErrorWithCode(fieldError.Field, fieldError.Description);
                    workspan.Log.Warning("Paysafe PaymentProvider failed: {Field} {Error}",
                        fieldError.Field,
                        fieldError.Description);
                }
            }

            workspan.Log.Information("Paysafe PaymentProvider failed with {ResponseSummary}",
                response.Error.Message);
        }
        else
        {
            result.ProviderResponseCode = "COMPLETED";
            result.ProviderResponseMessage = "COMPLETED";
        }

        result.RawResult = JsonConvert.SerializeObject(response, _jsonSerializeSettings);
        result.ProviderTransactionToken = response.Id;
    }

    private void ProcessException(Exception e, IResult result, Workspan workspan)
    {
        result.RawResult = JsonConvert.SerializeObject(e);
        result.Status = TransactionStatus.Failed;
        result.AddError(e.Message);

        workspan.RecordException(e);
    }


    private void LogResponse(object response, Workspan workspan,
        [System.Runtime.CompilerServices.CallerMemberName]
        string memberName = "")
    {
        if (response is not null)
        {
            try
            {
                workspan.Log.Information("Paysafe {Response} in {method} ",
                    JsonConvert.SerializeObject(response, _jsonSerializeSettings), memberName);
            }
            catch (Exception e)
            {
                workspan.Log.Information("Paysafe response log exception {Message}", e.Message);
            }
        }
    }

    private async Task<TRes> AuthorizeOrSaleAsync<TRes, TReq>(TReq authRequest, bool isSale,
        CancellationToken token = default)
        where TReq : IAuthorizationRequest
        where TRes : IAuthorizationResult, new()
    {
        using var workspan = Workspan.Start<PaysafePaymentProvider>().LogEnterAndExit();

        var (urlPrefix, userName, password) = await GetCredentialsAsync(authRequest.SupportedGateway.Sandbox,
            authRequest.SupportedGateway.Id, token);

        var card = authRequest.CreditCard;
        var billingAddress = authRequest.BillingAddress;
        var shippingAddress = authRequest.ShippingAddress;
        var threeDs = authRequest.ThreeDS;
        var reference = Guid.NewGuid();

        var useSchemeTransactionId = !authRequest.IsCit && authRequest.NetworkReferenceData?.TransactionId is not null;

        TRes retval = new();

        retval.OrderId = authRequest.OrderId;
        retval.Provider = this.CurrentPaymentProvider;
        retval.BinNumber = authRequest.CreditCard.Bin;
        retval.PaymentInstrumentId = authRequest.PaymentInstrumentId;
        retval.CavvCode = authRequest.ThreeDS?.AuthenticationValue;

        try
        {
            var request = new PaysafeAuthorizationRequest()
            {
                Amount = authRequest.Amount,
                Card = new CardWithNumber()
                {
                    Cvv = card.VerificationValue, // CIT indicator
                    CardNum = card.Number,
                    CardExpiry = new CardExpiry()
                    {
                        Month = card.Month,
                        Year = card.Year
                    }
                },
                Authentication = threeDs is null
                    ? null
                    : new Authentication3dsV2()
                    {
                        Eci = int.Parse(threeDs.EcommerceIndicator),
                        Cavv = threeDs.AuthenticationValue,
                        ThreeDResult =
                            Enum.Parse<Authentication3dsV2.ThreeDResultEnum>(threeDs.AuthenticationResponseStatus),
                        DirectoryServerTransactionId = threeDs.DirectoryServerTransactionId,
                        ThreeDSecureVersion = threeDs.ThreeDsVersion,
                    },
                Description = null,
                Keywords = new[] {$"OrderId={authRequest.OrderId}", $"MerchantId={authRequest.Mid}"},
                Profile = new()
                {
                    FirstName = card.FirstName?.TruncateAndRemoveAlphaNumeric(80),
                    LastName = card.LastName?.TruncateAndRemoveAlphaNumeric(80),
                    Email = billingAddress?.Email?.TruncateAndRemoveAlphaNumeric(255, false),
                    MerchantCustomerId = authRequest.OrderId.ToString()?.TruncateAndRemoveAlphaNumeric(100, false),
                },
                Recipient = null,
                AccorD = null,
                BillingDetails = billingAddress is null
                    ? null
                    : new()
                    {
                        City = billingAddress.City?.TruncateAndRemoveAlphaNumeric(40),
                        Country = billingAddress.Country?.TruncateAndRemoveAlphaNumeric(2),
                        Phone =
                            billingAddress.PhoneNumber?.RemoveNotDigits()?.TruncateAndRemoveAlphaNumeric(10),
                        State =
                            GeoHelper.GetStateCode(billingAddress.State?.TruncateAndRemoveAlphaNumeric(40),
                                billingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10)),
                        Zip = billingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10),
                        Street = billingAddress.Address1?.TruncateAndRemoveAlphaNumeric(50, false),
                        Street2 = billingAddress.Address2?.TruncateAndRemoveAlphaNumeric(50, false)
                    },
                CustomerIp = authRequest.Device?.IpAddress, //CIT indicator
                DupCheck = true,
                ShippingDetails = shippingAddress is null
                    ? null
                    : new()
                    {
                        City = shippingAddress.City?.TruncateAndRemoveAlphaNumeric(40),
                        Country = shippingAddress.Country?.TruncateAndRemoveAlphaNumeric(2),
                        State =
                            GeoHelper.GetStateCode(shippingAddress.State?.TruncateAndRemoveAlphaNumeric(40),
                                shippingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10)),
                        Zip = shippingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10),
                        Street = shippingAddress.Address1?.TruncateAndRemoveAlphaNumeric(50, false),
                        RecipientName = null,
                        ShipMethod = ShipMethod.O,
                        Carrier = null
                    },
                EntryMode = null,
                FundingTransaction = null,
                PreAuth = true, //todo-check!
                SettleWithAuth = isSale,
                MerchantRefNum = $"{authRequest.OrderId.ToString()}-{Guid.NewGuid().ToString()}",
                SplitPay = null,
                AirlineTravelDetails = null,
                CruiselineTravelDetails = null,
                StoredCredential = null,
                Level2Level3 = shippingAddress is null //not sure
                    ? null
                    : new()
                    {
                        DestinationCountry = shippingAddress.Country?.TruncateAndRemoveAlphaNumeric(2),
                        DestinationZip = shippingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10)
                    },
                PaymentFacilitator = null,
                MerchantDescriptor = authRequest.Descriptor?.Name is null ||
                                     string.IsNullOrWhiteSpace(authRequest.Descriptor.Name)
                    ? null
                    : new()
                    {
                        DynamicDescriptor =
                            authRequest.Descriptor.Name.TruncateAndRemoveAlphaNumeric(20),
                        Phone = authRequest.Descriptor?.Phone?.RemoveNotDigits()?.Length == 10
                            ? authRequest.Descriptor?.Phone?.RemoveNotDigits()?.TruncateAndRemoveAlphaNumeric(10)
                            : null
                    },

                TransactionIntent = null,
                ExternalAuthDetails = null, //todo-not surre
                SettleWithExternalAuth = false //otherwise it requires airline details
            };

            // if (useSchemeTransactionId)
            // {
            //     request.ExternalAuthDetails = new() // todo - check if no need in authorization code??
            //     {
            //         CardSchemeTransactionId = authRequest.NetworkReferenceData.TransactionId
            //     };
            //     request.SettleWithExternalAuth = true;
            //     workspan.Log.Information("Paysafe using scheme transaction id {SchemeTransactionId}",
            //         authRequest.NetworkReferenceData.TransactionId);
            // }


            if (useSchemeTransactionId)
            {
                request.StoredCredential = new() // todo - check if no need in authorization code??
                {
                    Type = CredentialType.TOPUP,
                    Occurrence = Occurrence.SUBSEQUENT,
                    ExternalInitialTransactionId = authRequest.NetworkReferenceData.TransactionId
                };

                workspan.Log.Information("Paysafe using scheme transaction id {SchemeTransactionId}",
                    authRequest.NetworkReferenceData.TransactionId);
            }


            LogRequest(authRequest, workspan, logRequest =>
            {
                var creditCard = logRequest.CreditCard;

                if (creditCard != null)
                {
                    creditCard.Number = creditCard.Number?.Substring(0, 8);
                    creditCard.VerificationValue = StringHelpers.MaskAll(creditCard.VerificationValue, '*');
                    logRequest.CreditCard = creditCard;
                }

                logRequest.Gateway = null;
            });

            var response = await _sdk.AuthorizeAsync(request, urlPrefix, userName, password, token);

            LogResponse(response, workspan);

            // bank returns the HELD status when it is not certain if decline or approve.
            // it is  brute force complete of risky transaction
            if (response.Status == Status.HELD)
            {
                CompleteAuthRequest completeRequest = new()
                {
                    Status = CompleteStatus.COMPLETED
                };
                var res = await _sdk.CompleteAuthorizationAsync(completeRequest, response.Id, urlPrefix, userName,
                    password, token);

                if (res.Status != CompleteStatus.COMPLETED)
                    throw new Exception("Paysafe -cannot complete transaction in held status");
            }

            UpdateResult(response, retval, authRequest.OrderId.ToString(), workspan);

            retval.SchemeTransactionIdUsed = useSchemeTransactionId;

            var resultCodeToMap = response.Error != null ? response.Error.Code : response.Status.ToString();

            var internalResponse = await PaysafeResponseMapper.GetMappedResponseAsync(resultCodeToMap, null);

            retval.InternalResponseCode = internalResponse?.MappedResponseCode;
            retval.InternalResponseMessage = internalResponse?.MappedResponseMessage;
            retval.InternalResponseGroup = internalResponse?.MappedResponseGroup.ToString();

            retval.TransactionId = reference; // to search? check it
            retval.AuthorizationCode = response.AuthCode;

            if (response.Error != null)
            {
                retval.Status = TransactionStatus.Failed;

                retval.CvvCode = response.CvvVerification switch
                {
                    CvvVerification.MATCH => "M",
                    CvvVerification.NO_MATCH => "N",
                    CvvVerification.UNKNOWN => "U",
                    CvvVerification.NOT_PROCESSED => "P"
                };

                // Paysafe always returns AVS match even if there is an issue
                retval.AvsCode = "U";

                if (response.Error.Code == "5068" &&
                    response.Error.FieldErrors?.Any(x => x.Field == "card.cvv") == true)
                {
                    if (string.IsNullOrEmpty(request.Card.Cvv))
                    {
                        // Unprocessed as CVV is not provided
                        retval.CvvCode = "U";
                    }
                    else
                    {
                        // CVV is incorrect
                        retval.CvvCode = "N";
                    }
                }
            }
            else
            {
                retval.AvsCode = response.AvsResponse switch
                {
                    AvsResponse.MATCH => "Y",
                    AvsResponse.NO_MATCH => "N",
                    AvsResponse.UNKNOWN => "U",
                    AvsResponse.MATCH_ZIP_ONLY => "P",
                    AvsResponse.MATCH_ADDRESS_ONLY => "A",
                    AvsResponse.NOT_PROCESSED => "R",
                };

                retval.CvvCode = response.CvvVerification switch
                {
                    CvvVerification.MATCH => "M",
                    CvvVerification.NO_MATCH => "N",
                    CvvVerification.UNKNOWN => "U",
                    CvvVerification.NOT_PROCESSED => "P"
                };

                retval.Status = response.Status switch
                {
                    _ when response.Error is not null => throw new FlexChargeException("Shouldn't not be here"),
                    Status.COMPLETED => TransactionStatus.Completed,
                    Status.CANCELLED => TransactionStatus.Canceled,
                    Status.FAILED => TransactionStatus.Failed,
                    Status.RECEIVED => TransactionStatus.Completed,
                    Status.PENDING => TransactionStatus.Completed,
                    Status.HELD => TransactionStatus.Completed, // not used
                };
            }
        }
        catch (Exception e)
        {
            ProcessException(e, retval, workspan);
        }

        return retval;
    }

    public override async Task<AuthResult> AuthorizeAsync(AuthorizationRequest authRequest,
        CancellationToken token = default)
    {
        return await AuthorizeOrSaleAsync<AuthResult, AuthorizationRequest>(authRequest, false, token);
    }

    public override async Task<CapturePaymentResult> CaptureAsync(CapturePaymentRequest captureRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaysafePaymentProvider>()
            .LogEnterAndExit();

        var (urlPrefix, userName, password) = await GetCredentialsAsync(captureRequest.SupportedGateway.Sandbox,
            captureRequest.SupportedGateway.Id, token);

        CapturePaymentResult retval = new();

        retval.Provider = this.CurrentPaymentProvider;
        retval.TransactionId = captureRequest.TransactionId;
        retval.PaymentInstrumentId = captureRequest.PaymentInstrumentId;

        try
        {
            SettlementRequest request = new()
            {
                Amount = captureRequest.Amount,
                MerchantRefNum = Guid.NewGuid().ToString(), // captureRequest.TransactionId.ToString(),
                DupCheck = true,
                AirlineTravelDetails = null,
                CruiselineTravelDetails = null,
                Splitpay = null,
                GatewayReconciliationId = captureRequest.TransactionId.ToString(),
            };

            LogRequest(captureRequest, workspan, logRequest => { logRequest.Gateway = null; });

            var response =
                await _sdk.CaptureAsync(request, captureRequest.TransactionToken, urlPrefix, userName, password, token);

            LogResponse(response, workspan);
            UpdateResult(response, retval, captureRequest.OrderId.ToString(), workspan);

            retval.Status = response.Status switch
            {
                _ when response.Error is not null => TransactionStatus.Failed,
                SettlementStatus.FAILED => TransactionStatus.Failed,
                SettlementStatus.COMPLETED => TransactionStatus.Completed,
                SettlementStatus.RECEIVED => TransactionStatus.Completed,
                SettlementStatus.EXPIRED => TransactionStatus.Failed,
                SettlementStatus.PENDING => TransactionStatus.Completed,
                SettlementStatus.INITIATED => TransactionStatus.Completed,
            };
            retval.ProcessorId = response.GatewayResponse?.Processor;
        }
        catch (Exception e)
        {
            ProcessException(e, retval, workspan);
        }

        return retval;
    }

    public override async Task<SaleResult> SaleAsync(SaleRequest request, CancellationToken token = default)
    {
        return await AuthorizeOrSaleAsync<SaleResult, SaleRequest>(request, true, token);
    }

    public override async Task<ICreditPaymentResult> StandaloneCreditAsync(ICreditPaymentRequest creditRequest,
        SupportedGateway supportedGateway,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaysafePaymentProvider>()
            .Baggage("GatewayId", creditRequest?.Gateway?.Id.ToString())
            .LogEnterAndExit();

        var (urlPrefix, userName, password) = await GetCredentialsAsync(creditRequest.SupportedGateway.Sandbox,
            creditRequest.SupportedGateway.Id, token);

        CreditPaymentResult result = new();
        result.Provider = this.CurrentPaymentProvider;
        result.InternalTransactionId = creditRequest.TransactionId;
        result.PaymentInstrumentId = creditRequest.PaymentInstrumentId;
        result.ProcessorId = creditRequest.SupportedGateway.ProcessorId;

        LogRequest(creditRequest, workspan, logRequest =>
        {
            var creditCard = logRequest.CreditCard;

            if (creditCard != null)
            {
                creditCard.Number = creditCard.Number?.Substring(0, 8);
                creditCard.VerificationValue = StringHelpers.MaskAll(creditCard.VerificationValue, '*');
                logRequest.CreditCard = creditCard;
            }

            logRequest.Gateway = null;
        });

        PaysafeStandaloneRefundResponse response = default;
        try
        {
            var card = creditRequest.CreditCard;

            var billingAddress = creditRequest.BillingAddress;

            if (creditRequest.NetworkTokenInfo is null)
            {
                PaysafeStandaloneRefundRequestWithCardNumber requestWithCardNumber = new()
                {
                    Amount = creditRequest.Amount,
                    Card = new CardWithNumber()
                    {
                        Cvv = card.VerificationValue,
                        CardNum = card.Number,
                        CardExpiry = new CardExpiry()
                        {
                            Month = card.Month,
                            Year = card.Year
                        }
                    }
                };
                EnrichRequest(creditRequest, requestWithCardNumber);


                response =
                    await _sdk.StandaloneRefundWithCardNumberAsync(requestWithCardNumber, urlPrefix, userName,
                        password,
                        token);
            }
            else
            {
                PaysafeStandaloneRefundRequestWithToken requestWithToken = new()
                {
                    Amount = creditRequest.Amount,
                    Card = new CardWithToken()
                    {
                        Cvv = creditRequest.NetworkTokenInfo.Cryptogram,
                        PaymentToken = creditRequest.NetworkTokenInfo.Token,
                    }
                };
                EnrichRequest(creditRequest, requestWithToken);
                response =
                    await _sdk.StandaloneRefundWithTokenAsync(requestWithToken, urlPrefix, userName,
                        password,
                        token);
            }

            LogResponse(response, workspan);
            UpdateResult(response, result, creditRequest.OrderId.ToString(), workspan);
            result.Status = response.Status switch
            {
                _ when response.Error is not null => TransactionStatus.Failed,
                Status.FAILED => TransactionStatus.Failed,
                Status.COMPLETED => TransactionStatus.Completed,
                Status.RECEIVED => TransactionStatus.Initialized,
                Status.PENDING => TransactionStatus.Completed,
                Status.CANCELLED => TransactionStatus.Completed
            };
            if (response.Status == Status.CANCELLED)
            {
                result.ProviderResponseCode = "VOID";
            }
        }
        catch (Exception e)
        {
            ProcessException(e, result, workspan);
        }

        return result;

        static void EnrichRequest(ICreditPaymentRequest creditRequest, IPaysafeStandaloneRefundRequest request)
        {
            var card = creditRequest.CreditCard;
            var billingAddress = creditRequest.BillingAddress;

            request.Amount = creditRequest.Amount;
            request.CustomerIp = creditRequest.Device?.IpAddress;
            request.Description = creditRequest.Reason;
            request.CustomerIp = creditRequest.Device?.IpAddress;

            request.Profile = new()
            {
                FirstName = card.FirstName?.TruncateAndRemoveAlphaNumeric(80),
                LastName = card.LastName?.TruncateAndRemoveAlphaNumeric(80),
                Email = billingAddress?.Email?.TruncateAndRemoveAlphaNumeric(255, false),
                MerchantCustomerId = creditRequest.OrderId.ToString()
            };

            request.BillingDetails = billingAddress is null
                ? null
                : new()
                {
                    City = billingAddress.City?.TruncateAndRemoveAlphaNumeric(40),
                    Country = billingAddress.Country?.TruncateAndRemoveAlphaNumeric(2),
                    Phone = billingAddress.PhoneNumber?.RemoveNotDigits()?.TruncateAndRemoveAlphaNumeric(40),
                    State = GeoHelper.GetStateCode(billingAddress.State?.TruncateAndRemoveAlphaNumeric(40),
                        billingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10)),
                    Zip = billingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10),
                    Street = billingAddress.Address1?.TruncateAndRemoveAlphaNumeric(50),
                    Street2 = billingAddress.Address2?.TruncateAndRemoveAlphaNumeric(50)
                };

            request.DupCheck = true;
            request.MerchantRefNum = $"{creditRequest.OrderId.ToString()}_{Guid.NewGuid().ToString()}";
            request.GatewayReconciliationId = Guid.NewGuid().ToString();
        }
    }

    public override async Task<ICreditPaymentResult> CreditAsync(ICreditPaymentRequest creditRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaysafePaymentProvider>().LogEnterAndExit();

        var (urlPrefix, userName, password) = await GetCredentialsAsync(creditRequest.SupportedGateway.Sandbox,
            creditRequest.SupportedGateway.Id, token);
        CreditPaymentResult result = new();
        result.Provider = this.CurrentPaymentProvider;
        result.InternalTransactionId = creditRequest.TransactionId;
        result.PaymentInstrumentId = creditRequest.PaymentInstrumentId;
        result.ProcessorId = creditRequest.SupportedGateway.ProcessorId;

        LogRequest(creditRequest, workspan, logRequest =>
        {
            var creditCard = logRequest.CreditCard;

            if (creditCard != null)
            {
                creditCard.Number = creditCard.Number?.Substring(0, 8);
                creditCard.VerificationValue = StringHelpers.MaskAll(creditCard.VerificationValue, '*');
                logRequest.CreditCard = creditCard;
            }

            logRequest.Gateway = null;
        });

        try
        {
            var settlement = await _sdk.GetSettlementByIdAsync(creditRequest.ProviderTransactionToken, urlPrefix,
                userName, password, token);

            if (settlement is not null)
            {
                workspan.Log.Information("Paysafe settlement Status: {Status} AvailableToRefund: {AvailableToRefund}",
                    settlement.Status, settlement.AvailableToRefund);
            }

            if (settlement?.AvailableToRefund < creditRequest.Amount)
            {
                throw new Exception("Paysafe - cannot refund more than settled");
            }

            var toRefund = false;
            if (settlement is null)
            {
                workspan.Log.Warning("Paysafe settlement is null");
                toRefund = true;
            }
            else if (settlement.AvailableToRefund > creditRequest.Amount)
            {
                // partial refund - can't use cancel settlement
                toRefund = true;
            }
            else if (settlement.Status == SettlementStatus.RECEIVED
                     || settlement.Status == SettlementStatus.PENDING
                     || settlement.Status == SettlementStatus.INITIATED
                     || settlement.Status == SettlementStatus.PROCESSING
                    )
            {
                CancelSettlementRequest cr = new()
                {
                    Status = CompleteStatus.CANCELLED
                };

                var cancelResponse = await _sdk.CancelSettlementAsync(cr, creditRequest.ProviderTransactionToken,
                    urlPrefix,
                    userName, password, token);
                LogResponse(cancelResponse, workspan);

                if (cancelResponse.Status == CompleteStatus.CANCELLED) // cancel succeeded
                {
                    result.ProviderResponseCode = "VOID";
                    result.ProviderResponseMessage = "VOID";
                    result.Status = TransactionStatus.Completed;
                    result.RawResult = JsonConvert.SerializeObject(cancelResponse, _jsonSerializeSettings);
                    LogResponse(cancelResponse, workspan);
                    result.ProviderTransactionToken = cancelResponse.Id;
                    workspan.Log.Information("Capture with {ProviderToken} voided",
                        creditRequest.ProviderTransactionToken);
                }
                else
                {
                    toRefund = true;
                }
            }
            else
            {
                toRefund = true;
            }

            if (toRefund)
            {
                PaysafeRefundRequest request = new()
                {
                    Amount = creditRequest.Amount,
                    MerchantRefNum = $"{creditRequest.TransactionId.ToString()}_{Guid.NewGuid().ToString()}",
                    DupCheck = true,
                    Splitpay = null,
                    GatewayReconciliationId = Guid.NewGuid().ToString(),
                };

                PaysafeRefundResponse response =
                    await _sdk.RefundAsync(request, creditRequest.ProviderTransactionToken, urlPrefix, userName,
                        password,
                        token);
                LogResponse(response, workspan);

                UpdateResult(response, result, creditRequest.OrderId.ToString(), workspan);

                result.Status = response.Status switch
                {
                    _ when response.Error is not null => TransactionStatus.Failed,
                    Status.FAILED => TransactionStatus.Failed,
                    Status.COMPLETED => TransactionStatus.Completed,
                    Status.RECEIVED => TransactionStatus.Initialized,
                    Status.PENDING => TransactionStatus.Completed,
                    Status.CANCELLED => TransactionStatus.Completed
                };

                if (response.Status == Status.CANCELLED)
                {
                    result.ProviderResponseCode = "VOID";
                }
            }
        }
        catch (Exception e)
        {
            ProcessException(e, result, workspan);
        }

        return result;
    }

    public override async Task<IVoidPaymentResult> VoidAsync(IVoidPaymentRequest voidPaymentRequest,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaysafePaymentProvider>().LogEnterAndExit();

        var (urlPrefix, userName, password) = await GetCredentialsAsync(voidPaymentRequest.SupportedGateway.Sandbox,
            voidPaymentRequest.SupportedGateway.Id, token);

        var authorization = await _sdk.GetAuthorizationByIdAsync(voidPaymentRequest.ProviderTransactionToken, urlPrefix,
            userName, password, token);

        var toVoid = false;

        VoidPaymentResult result = new();
        result.ProcessorId = voidPaymentRequest.SupportedGateway.ProcessorId;
        result.Provider = this.CurrentPaymentProvider;
        result.PaymentInstrumentId = voidPaymentRequest.PaymentInstrumentId;

        if (authorization is not null && authorization.Status != Status.COMPLETED)
        {
            try
            {
                CompleteAuthRequest completeRequest = new()
                {
                    Status = CompleteStatus.CANCELLED
                };

                var res = await _sdk.CompleteAuthorizationAsync(completeRequest, authorization.Id, urlPrefix, userName,
                    password, token);
                if (res.Status == CompleteStatus.CANCELLED)
                {
                    LogResponse(res, workspan);
                    result.Status = TransactionStatus.Completed;
                    result.ProviderResponseCode = "COMPLETED";
                    result.ProviderResponseMessage = "COMPLETED";
                }
                else
                {
                    toVoid = true;
                }
            }
            catch (Exception e)
            {
                ProcessException(e, result, workspan);
                toVoid = true;
            }
        }
        else
        {
            toVoid = true;
        }

        if (toVoid)
        {
            LogRequest(voidPaymentRequest, workspan, logreq => logreq.Gateway = null);

            try
            {
                PaysafeVoidRequest request = new()
                {
                    MerchantRefNum = Guid.NewGuid().ToString(), //  voidPaymentRequest.TransactionId.ToString(),
                    DupCheck = true,
                    Amount = voidPaymentRequest.Amount,
                };

                var response =
                    await _sdk.VoidAsync(request, voidPaymentRequest.ProviderTransactionToken, urlPrefix, userName,
                        password, token);
                LogResponse(response, workspan);

                UpdateResult(response, result, voidPaymentRequest.OrderId.ToString(), workspan);

                result.Status = response.Status switch
                {
                    _ when response.Error is not null => TransactionStatus.Failed,
                    PaysafeVoidStatus.FAILED => TransactionStatus.Failed,
                    PaysafeVoidStatus.COMPLETED => TransactionStatus.Completed,
                    PaysafeVoidStatus.RECEIVED => TransactionStatus.Initialized,
                };

                result.ProviderTransactionToken = response.Id;
                result.RawResult = System.Text.Json.JsonSerializer.Serialize(response);
                result.ProviderResponseMessage = response.Status.ToString();
            }
            catch (Exception e)
            {
                ProcessException(e, result, workspan);
            }
        }

        return result;
    }

    public override async Task<IVerifyInstrumentResult> VerifyAsync(IVerifyInstrumentRequest payload,
        CancellationToken token = default)
    {
        using var workspan = Workspan.Start<PaysafePaymentProvider>().LogEnterAndExit();

        var (urlPrefix, userName, password) =
            await GetCredentialsAsync(payload.SupportedGateway.Sandbox, payload.SupportedGateway.Id, token);

        VerifyInstrumentResult retval = new();
        retval.OrderId = payload.OrderId;
        retval.Provider = this.CurrentPaymentProvider;
        retval.BinNumber = payload.CreditCard.Bin;
        retval.PaymentInstrumentId = payload.PaymentInstrumentId;
        retval.CavvCode = payload.ThreeDS?.AuthenticationValue;

        LogRequest(payload, workspan, logRequest =>
        {
            var creditCard = logRequest.CreditCard;

            if (creditCard != null)
            {
                creditCard.Number = creditCard.Number?.Substring(0, 8);
                creditCard.VerificationValue = StringHelpers.MaskAll(creditCard.VerificationValue, '*');
                logRequest.CreditCard = creditCard;
            }

            logRequest.Gateway = null;
        });

        try
        {
            var card = payload.CreditCard;
            var billingAddress = payload.BillingAddress;
            var reference = Guid.NewGuid();

            var request = new PaysafeVerificationRequest()
            {
                Card = new CardWithNumber()
                {
                    Cvv = card.VerificationValue,
                    CardNum = card.Number,
                    CardExpiry = new CardExpiry()
                    {
                        Month = card.Month,
                        Year = card.Year
                    }
                },
                CustomerIp = payload.Device?.IpAddress,
                Description = null,
                Profile = new()
                {
                    FirstName = card.FirstName?.TruncateAndRemoveAlphaNumeric(80),
                    LastName = card.LastName?.TruncateAndRemoveAlphaNumeric(80),
                    Email = billingAddress?.Email?.TruncateAndRemoveAlphaNumeric(255, false),
                    MerchantCustomerId = payload.OrderId.ToString()
                },
                BillingDetails = billingAddress is null
                    ? null
                    : new()
                    {
                        City = billingAddress.City?.TruncateAndRemoveAlphaNumeric(40),
                        Country = billingAddress.Country?.TruncateAndRemoveAlphaNumeric(2),
                        Phone = billingAddress.PhoneNumber?.RemoveNotDigits()?.TruncateAndRemoveAlphaNumeric(40),
                        State = GeoHelper.GetStateCode(billingAddress.State?.TruncateAndRemoveAlphaNumeric(40),
                            billingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10)),
                        Zip = billingAddress.Zip?.TruncateAndRemoveAlphaNumeric(10),
                        Street = billingAddress.Address1?.TruncateAndRemoveAlphaNumeric(50, false),
                        Street2 = billingAddress.Address2?.TruncateAndRemoveAlphaNumeric(50, false)
                    },

                DupCheck = true,
                MerchantRefNum = $"{payload.OrderId.ToString()}_{Guid.NewGuid().ToString()}",
                StoredCredential = null
            };

            var response = await _sdk.VerifyAsync(request, urlPrefix, userName, password, CancellationToken.None);
            LogResponse(response, workspan);
            UpdateResult(response, retval, payload.OrderId.ToString(), workspan);

            retval.TransactionId = reference; // to search? check it
            retval.AuthorizationCode = response.AuthCode;
            retval.AvsCode = response.AvsResponse switch
            {
                AvsResponse.MATCH => "Y",
                AvsResponse.NO_MATCH => "N",
                AvsResponse.UNKNOWN => "U",
                AvsResponse.MATCH_ZIP_ONLY => "P",
                AvsResponse.MATCH_ADDRESS_ONLY => "A",
                AvsResponse.NOT_PROCESSED => "R",
            };

            retval.CvvCode = response.CvvVerification switch
            {
                CvvVerification.MATCH => "M",
                CvvVerification.NO_MATCH => "N",
                CvvVerification.UNKNOWN => "U",
                CvvVerification.NOT_PROCESSED => "P"
            };
            retval.Status = response.Status switch
            {
                _ when response.Error is not null => TransactionStatus.Failed,
                VerificationStatus.COMPLETED => TransactionStatus.Completed,
                VerificationStatus.FAILED => TransactionStatus.Failed,
                VerificationStatus.RECEIVED => TransactionStatus.Completed, // TransactionStatus.Initialized,
            };
        }
        catch (Exception e)
        {
            ProcessException(e, retval, workspan);
        }

        return retval;
    }

    public override async Task<(bool CanHandle, Guid? SupportedGatewayId)> CanHandleWebhookEventAsync(string body,
        IHeaderDictionary headers, CancellationToken token)
    {
        return (false, null);
    }

    public override async Task<IRefundCancelResult> CancelRefundAsync(ICancelRefundRequest payload,
        CancellationToken token)
    {
        using var workspan = Workspan.Start<PaysafePaymentProvider>().LogEnterAndExit();

        var isSandbox = !EnvironmentHelper.IsInProduction;

        var (urlPrefix, userName, password) =
            await GetCredentialsAsync(isSandbox, payload.SupportedGatewayId, token);

        RefundCancelResult result = new();

        result.Provider = this.CurrentPaymentProvider;

        if (!payload.IsStandaloneRefund)
        {
            PaysafeCancelRefundRequest request = new()
            {
                Status = CompleteStatus.CANCELLED
            };

            var response = await _sdk.CancelRefundAsync(request, payload.ProviderToken, urlPrefix, userName,
                password, token);

            LogResponse(response, workspan);
            UpdateResult(response, result, payload.OrderId.ToString(), workspan);
            result.Success = response.Status == CompleteStatus.CANCELLED;
        }
        else
        {
            PaysafeCancelStandaloneRefundRequest request = new()
            {
                Status = CompleteStatus.CANCELLED
            };

            var response = await _sdk.CancelStandaloneRefundAsync(request, payload.ProviderToken, urlPrefix,
                userName,
                password, token);

            LogResponse(response, workspan);
            UpdateResult(response, result, payload.OrderId.ToString(), workspan);
            result.Success = response.Status == CompleteStatus.CANCELLED;
        }

        return result;
    }

    private async Task<List<Transaction>> GetTransactionsByRefNumber(string reference)
    {
        var result = await _context.Transactions
            .Where(e => e.Meta.RootElement.GetProperty("MerchantRefNum").GetString() == reference)
            .ToListAsync();
        return result;
    }

    public override bool SupportCapture => true;
    public override bool SupportPartiallyRefund => true;
    public override bool SupportRefund => true;
    public override bool SupportVoid => true;
    public override bool SupportTokenization => true;
    public override bool RequirePostProcessPayment => true;
    public override bool SupportPayouts => false;
    public override string CurrentPaymentProvider => GatewayTypesConstants.Paysafe;
    public override bool SupportsAch => false;
    public override bool SupportsCreditCards => true;
    public override bool SupportsCreditCardVerification => true;
    public override bool SupportsSubscription => false;

    public override bool SupportsStandaloneCredit => true;

    public override bool SupportsExternalThreeDS => true;
    public override CardBrand[] NetworkTokenCardBrands => null;
}