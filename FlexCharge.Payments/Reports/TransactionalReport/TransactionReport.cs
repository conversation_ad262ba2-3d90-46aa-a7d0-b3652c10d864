using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlexCharge.Common.Reporting;
using FlexCharge.Payments.Entities;
using FlexCharge.Payments.Reports.TransactionalReport;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace FlexCharge.Payments.Reports;

public class TransactionReport : ReportBase<TransactionReportParam, TransactionReportOptions, TransactionReportModel>
{
    public async Task<List<TransactionReportModel>> MainQuery(DateTime from, DateTime to,
        TransactionReportParam transactionReportParam, CancellationToken ctoken)
    {
        var timezoneName = transactionReportParam.Timezone!;
        if (!string.IsNullOrWhiteSpace(timezoneName))
        {
            var timezone = TimeZoneInfo.FindSystemTimeZoneById(timezoneName);
            from = DateTime.SpecifyKind(from, DateTimeKind.Unspecified);
            to = DateTime.SpecifyKind(to, DateTimeKind.Unspecified);
            from = TimeZoneInfo.ConvertTimeToUtc(from, timezone);
            to = TimeZoneInfo.ConvertTimeToUtc(to, timezone);
        }

        var ctx = _dbContext as PostgreSQLDbContext;
        var query = ctx.Transactions
            .Where(t =>
                //t.Amount > 0
                t.Status == TransactionStatus.Completed
                &&
                t.CreatedOn >= from && t.CreatedOn <= to)
            //.Where(t =>  if (transReportParam?.Mid.HasValue==true)t.Merchant.Id == transReportParam.Mid.Value)
            .GroupJoin(ctx.PaymentInstruments,
                p => p.PaymentMethodId,
                t => t.Id,
                (trx, pmt) => new {trx, pmt})
            .SelectMany(
                x => x.pmt.DefaultIfEmpty(),
                (trx, pmt) => new {trx, pmt}
            )
            .GroupJoin(ctx.Merchants, // cannot be null, replace later
                t => t.trx.trx.Merchant.Id,
                m => m.Id,
                (t, m) => new {t, m})
            .SelectMany(
                x => x.m.DefaultIfEmpty(),
                (trx, m) => new {trx = trx.t.trx.trx, pmt = trx.t.pmt, mrch = m}
            )
            .Select(z => new TransactionReportModel()
            {
                CreatedOn = string.IsNullOrEmpty(timezoneName)
                    ? z.trx.CreatedOn
                    : TimeZoneInfo.ConvertTimeBySystemTimeZoneId(z.trx.CreatedOn, timezoneName),
                Id = z.trx.Id,
                ProviderName = z.trx.ProviderName,
                ProviderTransactionToken = "=\"" + z.trx.ProviderTransactionToken + "\"",
                OrderId = z.trx.OrderId,
                DynamicDescriptor = z.trx.DynamicDescriptor,
                AmountOriginal = z.trx.Amount,
                Currency = z.trx.Currency,
                Status = z.trx.Status.ToString(),
                Type = z.trx.Type,
                PaymentType = z.trx.PaymentType,
                ProcessorName = z.trx.ProcessorName,
                ProcessorId = z.trx.ProcessorId,
                CardHolderName = z.pmt.CardHolderName,
                Last4 = "=\"" + z.pmt.Last4 + "\"",
                Mid = z.trx.Merchant.Mid,
                Dba = z.mrch.Dba,
                CompanyName = z.mrch.CompanyName,
                Bin = "=\"" + z.pmt.Bin + "\"",
                ResponseCode = z.trx.ResponseCode,
                ResponseMessage = z.trx.ResponseMessage,
                CAvvResultCode = z.trx.CavvResultCode,
                CvvResultCode = z.trx.CvvResultCode,
                AvsResultCode = z.trx.AvsResultCode,
                AuthorizationId = z.trx.AuthorizationId,
                Pid = z.mrch.Pid
            });


        if (transactionReportParam != null)
        {
            if (transactionReportParam.ProviderName != null)
                query = query.Where(z => z.ProviderName.ToLower() == transactionReportParam.ProviderName);
            if (transactionReportParam?.Mid.HasValue == true)
                query = query.Where(z => z.Mid == transactionReportParam.Mid.Value);
            if (transactionReportParam?.Pid.HasValue == true)
                query = query.Where(z => z.Pid == transactionReportParam.Pid.Value);
            if (transactionReportParam.PaymentType != null)
                query = query.Where(z => z.PaymentType == transactionReportParam.PaymentType);
            if (transactionReportParam.TransactionType != null)
                query = query.Where(z => z.Type.ToLower() == transactionReportParam.TransactionType);
        }

        query = query.OrderByDescending(x => x.CreatedOn);
        // var qs = query.ToQueryString();

        return await query.ToListAsync(ctoken);
    }

    public string AvsResultCode { get; set; }

    public TransactionReport(IOptions<TransactionReportOptions> options, DbContext dbcontext)
        : base(options, dbcontext)
    {
        // _options = options;
        // _dbContext = dbcontext as PostgreSQLDbContext;
    }


    protected override bool TryValidate(TransactionReportParam parameter, out ReportValidator validator)
    {
        void validateTimeZone()
        {
            var tz = parameter.Timezone;
            if (!string.IsNullOrWhiteSpace(tz))
            {
                tz = tz.Trim();
                parameter.Timezone = tz;
                try
                {
                    TimeZoneInfo.FindSystemTimeZoneById(tz);
                }
                catch (TimeZoneNotFoundException ex)
                {
                    this.Validator.AddError("Timezone not found");
                }
            }
        }

        validateTimeZone();
        validator = this.Validator;
        return this.Validator.IsValid;
    }

    public override async Task<List<TransactionReportModel>> GenerateRowsAsync(DateTime startDate, DateTime endDate,
        TransactionReportParam param, CancellationToken ctoken)
    {
        var retval = await MainQuery(startDate, endDate, param, ctoken);
        return retval;
    }

    public override IAsyncEnumerable<TransactionReportModel> GenerateRowsYieldAsync(DateTime from, DateTime to,
        TransactionReportParam param,
        CancellationToken ctoken)
    {
        throw new NotImplementedException();
    }
}