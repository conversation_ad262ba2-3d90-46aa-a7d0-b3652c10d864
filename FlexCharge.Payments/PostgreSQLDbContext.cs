using FlexCharge.Payments.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using EntityFramework.Exceptions.PostgreSQL;
using FlexCharge.Utils;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.DependencyInjection;

namespace FlexCharge.Payments
{
    public class PostgreSQLDbContext : AuditableDbContext<PostgreSQLDbContext>
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public PostgreSQLDbContext()
        {
        }

        public PostgreSQLDbContext(DbContextOptions<PostgreSQLDbContext> options,
            IHttpContextAccessor httpContextAccessor,
            IServiceProvider serviceProvider)
            : base(options, serviceProvider)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseExceptionProcessor();
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            builder.Entity<Merchant>().Property<bool>("IsDeleted");
            builder.Entity<Merchant>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<Gateway>().Property<bool>("IsDeleted");
            builder.Entity<Gateway>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<SupportedGateway>().Property<bool>("IsDeleted");
            builder.Entity<SupportedGateway>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
            builder.Entity<SupportedGateway>().HasIndex(c => c.Name).IsUnique()
                .HasFilter("\"IsDeleted\"=false");

            builder.Entity<CycleMetrics>().HasIndex(c => c.SupportGatewayId).IsUnique()
                .HasFilter("\"IsDeleted\"=false");
            builder.Entity<CycleMetrics>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<ProviderMeta>().Property<bool>("IsDeleted");
            builder.Entity<ProviderMeta>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<PaymentInstrument>().Property<bool>("IsDeleted");
            builder.Entity<PaymentInstrument>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);


            builder.Entity<Batch>().Property<bool>("IsDeleted");
            builder.Entity<Batch>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<Transaction>().Property<bool>("IsDeleted");
            builder.Entity<Transaction>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<PartnerTransaction>().Property<bool>("IsDeleted");
            builder.Entity<PartnerTransaction>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            // builder.  ("DROP DATABASE [master]"); // <<< Anything you want :)
            // builder.Entity<Transaction>()
            //  .Property(p => p.CreatedOnDate);
            //.HasComputedColumnSql("toDate(\"CreatedOn\")");

            // builder.Entity<Activity>().Property<bool>("IsDeleted");
            // builder.Entity<Activity>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<OpenBankingData>().Property<bool>("IsDeleted");
            builder.Entity<OpenBankingData>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<FinancialAccount>().Property<bool>("IsDeleted");
            builder.Entity<FinancialAccount>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<OpenBankingAccessToken>().Property<bool>("IsDeleted");
            builder.Entity<OpenBankingAccessToken>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<Dispute>().Property<bool>("IsDeleted");
            builder.Entity<Dispute>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<DisputeActivity>().Property<bool>("IsDeleted");
            builder.Entity<DisputeActivity>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<DisputeQueueItem>().Property<bool>("IsDeleted");
            builder.Entity<DisputeQueueItem>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<ProviderService>().Property<bool>("IsDeleted");
            builder.Entity<ProviderService>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<PotentialMatch>().Property<bool>("IsDeleted");
            builder.Entity<PotentialMatch>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<ProviderService>()
                .Property(e => e.ProviderType)
                .HasEnumToStringConversion();
            // .HasConversion(r => r.ToString(),
            //     v => Enum.Parse<ProviderType>(v))
            // .UsePropertyAccessMode(PropertyAccessMode.Field);


            builder.Entity<ProviderService>()
                .Property(e => e.NameIdentifier)
                .HasEnumToStringConversion();
            // .HasConversion(r => r.ToString(),
            //     v => Enum.Parse<NameIdentifier>(v))
            // .UsePropertyAccessMode(PropertyAccessMode.Field);


            builder.Entity<ReportsHistory>().Property<bool>("IsDeleted");
            builder.Entity<ReportsHistory>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<AlertProvider>().Property<bool>("IsDeleted");
            builder.Entity<AlertProvider>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);

            builder.Entity<SupportedGateway>().Property<bool>("IsDeleted");
            builder.Entity<PaymentInstrument>()
                .Property(b => b.NetworkTokenEnabled)
                .HasDefaultValue(true);
            builder
                .HasPostgresEnum<PaymentMethodType>()
                .HasPostgresEnum<TransactionStatus>();

            builder.Entity<StripeBillingPortalData>().Property<bool>("IsDeleted");
            builder.Entity<StripeBillingPortalData>().HasQueryFilter(m => EF.Property<bool>(m, "IsDeleted") == false);
        }

        public DbSet<ProviderService> ProviderServices { get; set; }
        public DbSet<Merchant> Merchants { get; set; }
        public DbSet<SupportedGateway> SupportedGateways { get; set; }
        public DbSet<ProviderMeta> ProvidersMeta { get; set; }
        public DbSet<Gateway> Gateways { get; set; }

        public DbSet<CycleMetrics> Metrics { get; set; }

        public DbSet<PaymentInstrument> PaymentInstruments { get; set; }
        public DbSet<Batch> Batches { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<MonitoredTransaction> TransactionMonitoring { get; set; }
        public DbSet<PartnerTransaction> PartnerTransactions { get; set; }
        public DbSet<MonitoredPartnerTransaction> PartnerTransactionMonitoring { get; set; }
        public DbSet<Dispute> Disputes { get; set; }
        public DbSet<OpenBankingData> OpenBankingData { get; set; }

        public DbSet<OpenBankingAccessToken> OpenBankingAccessTokens { get; set; }

        //public DbSet<Activity> Activities { get; set; }
        public DbSet<FinancialAccount> FinancialAccounts { get; set; }
        public DbSet<DisputeActivity> DisputeActivities { get; set; }
        public DbSet<DisputeQueue> DisputeQueues { get; set; }
        public DbSet<DisputeQueueItem> DisputeQueueItems { get; set; }

        public DbSet<Subscription> Subscriptions { get; set; }

        public DbSet<ReportsHistory> ReportsHistory { get; set; }
        public DbSet<ReportingConfiguration> ReportingConfigurations { get; set; }
        public DbSet<AlertProvider> AlertProviders { get; set; }
        public DbSet<PotentialMatch> PotentialMatches { get; set; }

        public DbSet<StripeBillingPortalData> StripeBillingPortalData { get; set; }

        public override int SaveChanges()
        {
            UpdateSoftDeleteStatuses();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess,
            CancellationToken cancellationToken = default(CancellationToken))
        {
            UpdateSoftDeleteStatuses();
            return await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        private void UpdateSoftDeleteStatuses()
        {
            var user = _httpContextAccessor?.HttpContext?.User.Claims.SingleOrDefault(x =>
                x.Type == ClaimTypes.NameIdentifier);
            foreach (var entry in ChangeTracker.Entries())
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }


                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                        if (entry.Properties.Any(o => o.Metadata.Name == "IsDeleted"))
                            entry.CurrentValues["IsDeleted"] = false;

                        break;
                    }
                    case EntityState.Added:
                    {
                        if (user != null)
                        {
                            if (entry.Properties.Any(o => o.Metadata.Name == "CreatedBy"))
                                entry.Property("CreatedBy").CurrentValue = user.Value;
                            if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedBy"))
                                entry.Property("ModifiedBy").CurrentValue = user.Value;
                        }

                        if (entry.Properties.Any(o => o.Metadata.Name == "CreatedOn") &&
                            entry.Property("CreatedOn").CurrentValue == null)
                            entry.Property("CreatedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                        if (entry.Properties.Any(o => o.Metadata.Name == "ModifiedOn"))
                            entry.Property("ModifiedOn").CurrentValue = DateTime.Now.ToUniversalTime();

                        if (entry.Properties.Any(o => o.Metadata.Name == "IsDeleted"))
                            entry.CurrentValues["IsDeleted"] = false;

                        break;
                    }

                    case EntityState.Deleted:
                        if (entry.Properties.Any(o => o.Metadata.Name == "IsDeleted"))
                        {
                            entry.State = EntityState.Modified;
                            entry.CurrentValues["IsDeleted"] = true;

                            // CascadeSoftDelete(entry);
                        }
                        else
                        {
                            entry.State = EntityState.Deleted;
                        }

                        break;
                }
            }
        }

        // /// <summary>
        // /// Cascades Soft Delete to Related Entities
        // /// </summary>
        // private void CascadeSoftDelete()
        // {
        //     //See: https://github.com/aspnetboilerplate/aspnetboilerplate/issues/3543
        //     
        //     foreach (var entry in ChangeTracker.Entries().ToList())
        //     {
        //         if (entry.State == EntityState.Deleted && entry.Entity is IEntity)
        //         {
        //             CascadeSoftDelete(entry);
        //         }
        //     }
        // }

        /// Cascades Soft Delete to Related Entities
        private void CascadeSoftDelete(EntityEntry entry)
        {
            var dependentToPrincipalNavigations =
                entry.Navigations.Where(x => !((IReadOnlyNavigation) x.Metadata).IsOnDependent).ToList();

            foreach (var navigationEntry in dependentToPrincipalNavigations)
            {
                //see: https://github.com/dotnet/efcore/issues/23283
                var foreignKey = ((IReadOnlyNavigation) navigationEntry.Metadata).ForeignKey;
                if (typeof(ISoftDeleteEntity).IsAssignableFrom(foreignKey.DeclaringEntityType.ClrType))
                {
                    if (foreignKey.DeleteBehavior == DeleteBehavior.Cascade)
                    {
                        if (!navigationEntry.IsLoaded)
                        {
                            navigationEntry.Load();
                        }

                        if (navigationEntry.CurrentValue != null)
                        {
                            switch (navigationEntry)
                            {
                                case CollectionEntry collectionEntry:
                                    foreach (var x in collectionEntry.CurrentValue)
                                    {
                                        ((ISoftDeleteEntity) Entry(x).Entity).IsDeleted = true;
                                        Entry(x).State = EntityState.Modified;

                                        //SetDeletionAuditProperties(entry.Entity, userId);
                                        //changeReport.ChangedEntities.Add(new EntityChangeEntry(entry.Entity, EntityChangeType.Deleted));
                                    }

                                    break;
                                case ReferenceEntry referenceEntry:
                                    ((ISoftDeleteEntity) Entry(referenceEntry.CurrentValue).Entity).IsDeleted = true;
                                    Entry(referenceEntry.CurrentValue).State = EntityState.Modified;

                                    //SetDeletionAuditProperties(entry.Entity, userId);
                                    //changeReport.ChangedEntities.Add(new EntityChangeEntry(entry.Entity, EntityChangeType.Deleted));

                                    break;
                            }
                        }
                    }
                }
            }
        }
    }
}