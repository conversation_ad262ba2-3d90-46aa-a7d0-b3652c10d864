using FlexCharge.Common.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using FlexCharge.Common.Shared.Orders;
using FlexCharge.Merchants.Entities;
using FlexCharge.Merchants.Enums;

namespace FlexCharge.Merchants.DTO
{
    public class MerchantResponse : BaseResponse
    {
        public MerchantResponse()
        {
        }

        public Guid Id { get; set; }

        public string CompanyName { get; set; }
        public string LegalEntityName { get; set; }
        public string Dba { get; set; }

        public string Type { get; set; }
        public string TaxId { get; set; }

        public string Descriptor { get; set; }

        public string Website { get; set; }

        public string Description { get; set; }
        public string SpecialTerms { get; set; }
        public string Mcc { get; set; }
        public string Industry { get; set; }
        public string EcommercePlatform { get; set; }

        public ActiveInActive Status { get; set; }
        public string StatusName => Enum.GetName(typeof(ActiveInActive), Status);

        public bool PayoutsEnabled { get; set; }
        public bool Locked { get; set; }

        public string BusinessEstablishedDate { get; set; }
        public bool Pcidss { get; set; }

        public string Icon { get; set; }
        public string Logo { get; set; }
        public string PrimaryColor { get; set; }
        public string SecondaryColor { get; set; }

        public decimal? GhostModeThrottlePercentage { get; set; }
        public decimal? DynamicAuthorizationDiscountThrottlePercentage { get; set; }
        public int OfferRequestsRateLimitIntervalMS { get; set; }
        public int OfferRequestsRateLimitCount { get; set; }
        public int OfferRequestsMaxPerDay { get; set; }
        public decimal OfferRequestsThrottlePercentage { get; set; }

        public MerchantCustomerSupportInformationDTO CustomerSupportInformation { get; set; }

        /// <summary>
        ///  % of NSF declines FlexCharge is willing to pass on evaluate
        /// </summary>
        public decimal? Offer_NSF_RequestsThrottle_Percentage { get; set; }

        /// <summary>
        /// Max value of offers FlexCharge is willing to approve and payout per month (In Cents)
        /// </summary>
        public int? Orders_MaxMonthlyAmount { get; set; }

        public bool IsBureauProductionActive { get; set; }

        public bool IsMitEnabled { get; set; }
        public bool IsMITEvaluateAsync { get; set; }
        public bool IsCITEvaluateAsync { get; set; }

        public DateTime CreatedOn { get; set; }

        public AddressDTO Address { get; set; }
        public PrimaryContact PrimaryContact { get; set; }
        public DeveloperContact DeveloperContact { get; set; }
        public PartnerQueryDTO Partner { get; set; }
        public PartnerQueryDTO IntegrationPartner { get; set; }
        public SalesAgencyQueryDTO SalesAgency { get; set; }

        public BankAccountInformationDTO BankAccountInformation { get; set; }

        public List<MerchantFundsReserveConfigurationDTO> FundsReserveConfigurations { get; set; }
        public IEnumerable<FeeConfigurationResponse> Fees { get; set; }
        public IEnumerable<SiteDTO> Sites { get; set; }

        public IDictionary<string, IGrouping<DateTime, FeeConfigurationResponse>> FeesHistory { get; set; }
        public bool IsSiteValidated { get; set; }
        public bool IsIntegrationGuideEnabled { get; set; }
        public int? Timezone { get; set; }
        public string? Language { get; set; }
        public string? TimezoneName { get; set; }
        public bool VirtualTerminalEnabled { get; set; }
        public bool BillingInformationOptional { get; set; }
        public bool MITGetSiteByDynamicDescriptorEnabled { get; set; }
        public bool MITConsumerNotificationsEnabled { get; set; }
        public bool MITConsumerCuresEnabled { get; set; }

        public bool? UIWidgetOptional { get; set; }
        public bool CITConsumerNotificationsEnabled { get; set; }
        public bool AllowBinCheckOnTokenization { get; set; }
        public bool EnableGlobalNetworkTokenization { get; set; }
        public bool CITClickToRefundEnabled { get; set; }
        public bool MITClickToRefundEnabled { get; set; }
        public bool UseDefaultSiteForUnknownMerchantUrlsEnabled { get; set; }
        public bool IsSenseJsOptional { get; set; }
        public Guid? AccountId { get; set; }

        public bool AccountUpdaterEnabled { get; set; }
        public bool Global3DSEnabled { get; set; }
        public bool InformationalOnly3DS { get; set; }

        public decimal MinOrderAmount { get; set; }
        public decimal MaxOrderAmount { get; set; }

        public int? MITAgreedExpiryHours { get; set; }

        public bool IsCrawlingEnabled { get; set; }
        public bool IsIframeMessagesCollectEnabled { get; set; }
        public bool IsEnforceMFAEnabled { get; set; }
        public bool CaptureRequired { get; set; }
        public bool SchemeTransactionIdEnabled { get; set; }
        public bool MITImmediateRetryEnabled { get; set; }

        public int? RiskLevel { get; set; }
        public int? RiskLevel_Visa { get; set; }
        public int? RiskTier { get; set; }

        public List<Owner>? Owners { get; set; }
        public string? ProductsSold { get; set; }

        public string? ConsumerOrderNotificationChannel { get; set; }

        public bool IsAvsRequired { get; set; }
        public bool IsCvvRequired { get; set; }
        public List<string> SupportedCountries { get; set; }
        public List<DocumentDTO> UploadedDocuments { get; set; }
        public bool IgnoreSiteIdFromClient { get; set; }
        public bool PayerEnabled { get; set; }
        public bool RedactIpEnabled { get; set; }
        public bool IntegrationPartnerParticipateSale { get; set; }
        public int AnnualSalesVolume { get; set; }
        public string CrmId { get; set; }
    }
}