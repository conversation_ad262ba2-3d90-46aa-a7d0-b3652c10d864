using System;
using FlexCharge.Merchants.Enums;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using FlexCharge.Common.Shared.Orders;
using FlexCharge.Merchants.DTO;

namespace FlexCharge.Merchants.Entities
{
    public class Merchant : AuditableEntity
    {
        public Account? Account { get; set; }
        public string CompanyName { get; set; }
        public string LegalEntityName { get; set; }
        public string Dba { get; set; }

        public string Type { get; set; }
        public string TaxId { get; set; }
        public string Descriptor { get; set; }
        public string Website { get; set; }

        public string Description { get; set; }
        public string SpecialTerms { get; set; }
        public string BusinessEstablishedDate { get; set; }
        public string Mcc { get; set; }
        public string Industry { get; set; }
        public bool Pcidss { get; set; }
        public string EcommercePlatform { get; set; }

        public string DdaType { get; set; }
        public string AccountNumber { get; set; }
        public string RoutingNumber { get; set; }
        public string Currency { get; set; }
        public string BankName { get; set; }
        public DateTime? BankAccountVerified { get; set; }

        public Guid? FinancialAccountId { get; set; }

        public ActiveInActive Status { get; set; }
        public string IntegrationType { get; set; }

        public bool PayoutsEnabled { get; set; }

        public bool Locked { get; set; }

        /// <summary>
        /// This risk level is used for all non-visa cards (Mastercard only for now)
        /// </summary>
        public int? RiskLevel { get; set; }

        public int? RiskLevel_Visa { get; set; }

        /// <summary>
        /// Used for payment provider matching tier can be 1,2,3
        /// </summary>
        public int? RiskTier { get; set; }

        public string Icon { get; set; }
        public string LogoUrl { get; set; }
        public string PrimaryColor { get; set; }
        public string SecondaryColor { get; set; }

        public decimal? GhostModeThrottlePercentage { get; set; }
        public int? OfferRequestsRateLimitIntervalMS { get; set; }
        public int? OfferRequestsRateLimitCount { get; set; }
        public int? OfferRequestsMaxPerDay { get; set; }
        public decimal? OfferRequestsThrottlePercentage { get; set; }

        /// <summary>
        ///  % of NSF declines FlexCharge is willing to pass on evaluate
        /// </summary>
        public decimal? Offer_NSF_RequestsThrottle_Percentage { get; set; }

        /// <summary>
        /// Max value of offers FlexCharge is willing to approve and payout per month (In Cents)
        /// </summary>
        public int? Orders_MaxMonthlyAmount { get; set; }

        public bool IsBureauProductionActive { get; set; } = false;
        public bool IsMitEnabled { get; set; } = false;
        public bool MITEvaluateAsync { get; set; } = true;
        public bool CITEvaluateAsync { get; set; } = false;

        public string CustomerSupportName { get; set; }
        public string CustomerSupportEmail { get; set; }
        public string CustomerSupportPhone { get; set; }
        public string CustomerSupportLink { get; set; }

        public List<Site> Sites { get; set; }
        public Address Address { get; set; }
        public List<MerchantFee> Fees { get; set; }

        public List<Report> Reports { get; set; }
        public Contact PrimaryContact { get; set; }
        public Contact DeveloperContact { get; set; }

        public Guid? PartnerId { get; set; }
        public Partner Partner { get; set; }

        public Guid? IntegrationPartnerId { get; set; }
        public Partner IntegrationPartner { get; set; }

        public Guid ApplicationId { get; set; }
        public Guid? SalesAgencyId { get; set; }
        public SalesAgency SalesAgency { get; set; }
        public bool IsIntegrationGuideEnabled { get; set; } = false;
        public bool IsSiteValidated { get; set; } = false;


        public int? Timezone { get; set; }
        public string? TimezoneName { get; set; }
        public string? Language { get; set; }
        public bool VirtualTerminalEnabled { get; set; } = false;
        public bool BillingInformationOptional { get; set; } = false;
        public bool MITGetSiteByDynamicDescriptorEnabled { get; set; } = false;
        public bool MITConsumerNotificationsEnabled { get; set; } = false;
        public bool MITConsumerCuresEnabled { get; set; } = false;

        public bool? UIWidgetOptional { get; set; }
        public bool CITConsumerNotificationsEnabled { get; set; } = false;
        public bool AllowBinCheckOnTokenization { get; set; } = false;
        public bool EnableGlobalNetworkTokenization { get; set; } = false;

        public bool CITClickToRefundEnabled { get; set; } = false;
        public bool MITClickToRefundEnabled { get; set; } = false;
        public int? MITAgreedExpiryHours { get; set; }
        public bool UseDefaultSiteForUnknownMerchantUrlsEnabled { get; set; } = false;
        public bool IsSenseJsOptional { get; set; } = false;
        public Guid? AccountId { get; set; }

        public int MinOrderAmount { get; set; } = 900;
        public int MaxOrderAmount { get; set; } = 30000;

        //This is realtime account updater 
        public bool AccountUpdaterEnabled { get; set; } = false;
        public bool IsCrawlingEnabled { get; set; } = false;
        public bool IsIframeMessagesCollectEnabled { get; set; } = false;
        public bool IsEnforceMFAEnabled { get; set; } = false;
        public bool CaptureRequired { get; set; } = false;
        public decimal? DynamicAuthorizationDiscountThrottlePercentage { get; set; } // Temporary config
        public string? PrivateSshKey { get; set; }
        public string? PublicSshKeyId { get; set; }


        public int LatestDisputeRate { get; set; }

        public List<MerchantFundsReserveConfiguration> FundsReserveConfigurations { get; set; }
        public List<Owner>? Owners { get; set; }
        public string? ProductsSold { get; set; }
        public bool Global3DSEnabled { get; set; } = false;
        public bool InformationalOnly3DS { get; set; } = false;
        public bool SchemeTransactionIdEnabled { get; set; } = false;
        public bool MITImmediateRetryEnabled { get; set; } = false;

        public ConsumerNotificationChannel? ConsumerOrderNotificationChannel { get; set; }

        public bool IsAvsRequired { get; set; } = false;
        public bool IsCvvRequired { get; set; } = false;

        public string SupportedCountries { get; set; }

        public bool IgnoreSiteIdFromClient { get; set; }
        public int AnnualSalesVolume { get; set; }
        public int AvgPurchaseAmount { get; set; }
        public string ChargebackPercentage { get; set; }
        public string ReturnsPercent { get; set; }
        public int AnnualCreditVolume { get; set; }
        public int AnnualDebitVolume { get; set; }
        public DateTime? AgreeToTerms { get; set; }
        public Guid Assignee { get; set; }
        public List<Document> Documents { get; set; }
        public List<ApplicationActivity> Activities { get; set; }
        public string SitesCount { get; set; }
        public string TimeInBusiness { get; set; }
        public string EcommercePlatformUsagePeriod { get; set; }
        public string Healthcare { get; set; }
        public string MaxSubDuration { get; set; }
        public string Regulated { get; set; }
        [Column(TypeName = "jsonb")] public List<string> TrafficAcquisitionChannels { get; set; }
        public string InfoDisclosed { get; set; }

        public string IntegrationTier { get; set; }

        public string RiskStatus { get; set; } = RiskAssessmentStatus.NotStarted.ToString();
        public string RiskCategory { get; set; }
        public string OperationsStatus { get; set; } = Enums.OperationsStatus.NotStarted.ToString();

        public Guid? AccountManagerId { get; set; }
        public string LegalEntityCountry { get; set; }
        public string TrialPeriod { get; set; }
        public string AvgDeliveryTime { get; set; }
        public string RoutingType { get; set; }
        public string AvgDeclineRate { get; set; }
        public string AvgDisputeRate { get; set; }
        public string AvgTransactionAmount { get; set; }

        public string AvgMonthlySales { get; set; }
        public bool PayerEnabled { get; set; } = false;
        public bool RedactIpEnabled { get; set; }

        public Guid? EligibilityStrategyWorkflowId { get; set; }
        public Guid? NotEligibleOrderProcessingWorkflowId { get; set; }
        public Guid? NotEligibleEverOrderProcessingWorkflowId { get; set; }
        public Guid? RecyclingStrategyWorkflowId { get; set; }

        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public bool IntegrationPartnerParticipateSale { get; set; }
        public string CrmId { get; set; }
    }
}