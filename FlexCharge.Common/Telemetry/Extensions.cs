//see: https://github.com/open-telemetry/opentelemetry-dotnet/tree/main

using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Diagnostics.Metrics;
using FlexCharge.Common.Jaeger;
using MassTransit.Logging;
using MassTransit.Monitoring;
using Microsoft.Extensions.Configuration;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace FlexCharge.Common.Telemetry
{
    // For tracing in Visual Studio:
    // see: https://www.meziantou.net/getting-telemetry-data-from-inside-or-outside-a-dotnet-application.htm

    public static class TelemetryServiceCollectionExtensions
    {
        private static readonly string JaegerSectionName = "jaeger";

        public static IServiceCollection AddTelemetry(this IServiceCollection services,
            bool enableConsoleExporter = false)
        {
            Activity.DefaultIdFormat = ActivityIdFormat.W3C;

            var activityListener = new ActivityListener
            {
                ShouldListenTo = _ => true,
                SampleUsingParentId = (ref ActivityCreationOptions<string> activityOptions) =>
                    //ActivitySamplingResult.AllData,
                    ActivitySamplingResult.AllDataAndRecorded,
                Sample = (ref ActivityCreationOptions<ActivityContext> activityOptions) =>
                    //ActivitySamplingResult.AllData
                    ActivitySamplingResult.AllDataAndRecorded
            };
            ActivitySource.AddActivityListener(activityListener);


// #if DEBUG
//             // This must be set before creating a GrpcChannel/HttpClient when calling an insecure service
//             AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2UnencryptedSupport", true);
// #endif


            IConfiguration configuration;
            using (var serviceProvider = services.BuildServiceProvider())
            {
                configuration = serviceProvider.GetService<IConfiguration>();
            }

            var appOptions = configuration.GetOptions<AppOptions>("app");

            var serviceName = appOptions.Name;
            var serviceVersion = appOptions.Version;

            Workspan.Initialize(serviceName, serviceVersion);

            // Not required if Serilog is used???
            // //see: https://github.com/newrelic/newrelic-opentelemetry-examples/blob/main/getting-started-guides/dotnet/README.md
            // //see: https://learn.microsoft.com/en-us/dotnet/core/diagnostics/distributed-tracing-collection-walkthroughs
            // //see: https://www.meziantou.net/getting-telemetry-data-from-inside-or-outside-a-dotnet-application.htm
            // services.AddLogging(builder =>
            // {
            //     builder
            //         .AddEventSourceLogger() // This is added automatically in the default ASP.NET Core template
            //         // Configure the OpenTelemetry SDK for logs
            //         .AddOpenTelemetry(options =>
            //         {
            //             options.IncludeFormattedMessage = true;
            //             options.ParseStateValues = true;
            //             options.IncludeScopes = true;
            //         });
            //
            //     #region Commented
            //
            //     // //see: https://www.meziantou.net/monitoring-a-dotnet-application-using-opentelemetry.htm
            //     // builder.AddOpenTelemetry(options =>
            //     // {
            //     //     options.IncludeFormattedMessage = true;
            //     //     options.IncludeScopes = true;
            //     //     options.ParseStateValues = true;
            //     //     //options.AddConsoleExporter()
            //     // });
            //
            //     #endregion
            // });


            var jaegerOptions = configuration.GetOptions<JaegerOptions>(JaegerSectionName);

            //see: https://github.com/newrelic/newrelic-opentelemetry-examples/blob/main/getting-started-guides/dotnet/README.md
            //see: https://learn.microsoft.com/en-us/dotnet/core/diagnostics/distributed-tracing-collection-walkthroughs
            services.AddOpenTelemetry()
                .UseOtlpExporter()
                .ConfigureResource(resourceBuilder =>
                {
                    resourceBuilder
                        .AddService(serviceName, serviceVersion,
                            serviceInstanceId: Environment.MachineName)
                        .AddEnvironmentVariableDetector();
                })
                .WithTracing(tracerProviderBuilder =>
                    {
                        tracerProviderBuilder
                            .AddSource(serviceName, serviceVersion)
                            .AddSource(DiagnosticHeaders.DefaultListenerName) // MassTransit activity source
                            .AddAspNetCoreInstrumentation()
                            .AddProcessor(new EnrichFromBaggageProcessor())
                            .SetSampler(new AlwaysOnSampler())
                            .SetErrorStatusOnException(true)
                            ;

                        if (enableConsoleExporter)
                        {
                            tracerProviderBuilder
                                .AddConsoleExporter();
                        }
                    }
                )
                .WithMetrics(meterProviderBuilder =>
                {
                    meterProviderBuilder
                        .AddMeter(serviceName)
                        .AddAspNetCoreInstrumentation()
                        .AddRuntimeInstrumentation()
                        .AddMeter(InstrumentationOptions.MeterName) /* If we want to add MassTransit Meters
                         (see: https://masstransit.io/documentation/configuration/observability)*/
                        .AddView(instrument =>
                        {
                            return instrument.GetType().GetGenericTypeDefinition() == typeof(Histogram<>)
                                ? new Base2ExponentialBucketHistogramConfiguration()
                                : null;
                        });
                });


            // services.AddOpenTelemetryTracing(tracerProviderBuilder =>
            // {
            //     //see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/src/OpenTelemetry/README.md#tracing-configuration
            //     //see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/docs/trace/customizing-the-sdk/README.md
            //
            //     tracerProviderBuilder
            //         .SetResourceBuilder(
            //             ResourceBuilder.CreateDefault()
            //                 .AddService(serviceName: serviceName, serviceVersion: serviceVersion)
            //                 .AddTelemetrySdk()
            //                 .AddEnvironmentVariableDetector())
            //         .AddSource(serviceName, serviceVersion)
            //         .AddSource(DiagnosticHeaders.DefaultListenerName) // MassTransit ActivitySource
            //         .AddAspNetCoreInstrumentation()
            //         .AddHttpClientInstrumentation()
            //
            //         #region Commented
            //
            //         //.AddSource("MassTransit") //see: https://issuemode.com/issues/open-telemetry/opentelemetry-dotnet-contrib/76607777
            //         //.AddMassTransitInstrumentation()
            //
            //         //.AddHttpClientInstrumentation()
            //         //.AddNpgsql()
            //         ////.AddSqlClientInstrumentation()
            //         //.AddConsoleExporter()
            //
            //         #endregion
            //
            //         #region [Commented] Add Jaeger Exporter
            //
            //         // .AddJaegerExporter(opt =>
            //         // {
            //         //     //see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/src/OpenTelemetry.Exporter.Jaeger/README.md
            //         //     opt.AgentHost = jaegerOptions.AgentHost;
            //         //     opt.AgentPort = jaegerOptions.AgentPort;
            //         //
            //         //     //see: https://github.com/open-telemetry/opentelemetry-dotnet/issues/2758
            //         //     opt.MaxPayloadSizeInBytes = 4096;
            //         //
            //         //     opt.ExportProcessorType = ExportProcessorType.Batch;
            //         //     opt.BatchExportProcessorOptions = new BatchExportProcessorOptions<Activity>()
            //         //     {
            //         //         MaxQueueSize = 2048,
            //         //         ScheduledDelayMilliseconds = 5000,
            //         //         ExporterTimeoutMilliseconds = 30000,
            //         //         MaxExportBatchSize = 512,
            //         //     };
            //         // })
            //
            //         #endregion
            //
            //         #region [Commented] Add Oltp Exporter
            //
            //         // .AddOtlpExporter(opt =>
            //         // {
            //         //     opt.Endpoint = new Uri("http://" + jaegerOptions.AgentHost + ":4317");
            //         //     opt.Protocol = OtlpExportProtocol.Grpc;
            //         // })
            //
            //         #endregion
            //
            //         #region Commented
            //
            //         // .AddOtlpExporter(opt =>
            //         // {
            //         //     //opt.Endpoint = new Uri("your-endpoint-here");
            //         //     opt.Protocol = OtlpExportProtocol.HttpProtobuf;
            //         // })
            //
            //         #endregion
            //
            //         .AddProcessor(new EnrichFromBaggageProcessor())
            //
            //         #region Commented
            //
            //         // // see: https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/trace/sdk.md#simple-processor
            //         // // see: https://github.com/open-telemetry/opentelemetry-dotnet/tree/main/docs/trace/extending-the-sdk
            //         //.AddProcessor(new SimpleLogRecordExportProcessor())
            //         //.AddProcessor(new SimpleActivityExportProcessor())
            //         //.AddProcessor(new BatchActivityExportProcessor(()) 
            //
            //         #endregion
            //
            //         .SetSampler(new AlwaysOnSampler())
            //         .SetErrorStatusOnException(true)
            //         ;
            // });

            #region Commented

            // //see: https://docs.microsoft.com/en-us/dotnet/core/diagnostics/metrics-collection
            // //see: https://www.meziantou.net/monitoring-a-dotnet-application-using-opentelemetry.htm
            // //see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/98cb28974af43fc893ab80a8cead6e2d4163e144/examples/AspNetCore/Startup.cs
            // //see: https://github.com/open-telemetry/opentelemetry-dotnet/blob/main/src/OpenTelemetry.Exporter.Prometheus/README.md
            // services.AddOpenTelemetryMetrics(builder =>
            // {
            //     builder.AddAspNetCoreInstrumentation();
            //     builder.AddHttpClientInstrumentation();
            //     
            //     builder.AddMeter(serviceName, serviceVersion)
            //          .AddMeter(InstrumentationOptions.MeterName) // MassTransit Meter
            //         ;
            //     
            //     builder.AddOtlpExporter(options =>
            //     {
            //         options.Endpoint = new Uri($"{jaegerOptions.AgentHost}:{jaegerOptions.AgentPort}");
            //         //options.Protocol = OtlpExportProtocol.Grpc // it's by default
            //     });
            //     
            //     //builder.AddPrometheusExporter();
            //     
            //     // builder.AddConsoleExporter((exporterOptions, metricReaderOptions) =>
            //     // {
            //     //     exporterOptions.Targets = ConsoleExporterOutputTargets.Console;
            //     //
            //     //     // The ConsoleMetricExporter defaults to a manual collect cycle.
            //     //     // This configuration causes metrics to be exported to stdout on a 10s interval.
            //     //     metricReaderOptions.MetricReaderType = MetricReaderType.Periodic;
            //     //     metricReaderOptions.PeriodicExportingMetricReaderOptions.ExportIntervalMilliseconds = 10000;
            //     // });
            // });
            //
            // app.UseOpenTelemetryPrometheusScrapingEndpoint();

            #endregion

            #region Commented

            // //see: https://thecloudblog.net/post/distributed-tracing-in-asp.net-core-with-jaeger-and-tye-part-1-distributed-tracing/
            // services.AddSingleton<ITracer>(sp =>
            // {
            //     var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
            //     var reporter = new RemoteReporter.Builder().WithLoggerFactory(loggerFactory).WithSender(new UdpSender())
            //         .Build();
            //     var tracer = new Tracer.Builder(serviceName)
            //         // The constant sampler reports every span.
            //         .WithSampler(new ConstSampler(true))
            //         // LoggingReporter prints every reported span to the logging framework.
            //         .WithReporter(reporter)
            //         .Build();
            //     return tracer;
            // });

            #endregion

            return services;
        }

        private class EnrichFromBaggageProcessor : BaseProcessor<Activity>
        {
            //see: https://github.com/open-telemetry/opentelemetry-dotnet/tree/main/docs/trace/extending-the-sdk
            public override void OnEnd(Activity activity)
            {
                foreach (var baggage in activity.Baggage)
                {
                    activity.SetTag(baggage.Key, baggage.Value);
                }
            }
        }
    }
}