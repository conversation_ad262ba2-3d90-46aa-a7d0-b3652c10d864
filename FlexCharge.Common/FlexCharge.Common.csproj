<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
      <TargetFramework>net9.0</TargetFramework>
    <Configurations>Debug;Release;Staging</Configurations>
    <WarningsAsErrors>CS4014</WarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" Version="5.2.0"/>
    <PackageReference Include="AutoMapper" Version="14.0.0"/>
      <PackageReference Include="AutoMapper.Collection" Version="10.0.0"/>
    <PackageReference Include="AWSSDK.Athena" Version="3.7.7.3"/>
    <PackageReference Include="AWSSDK.CloudWatch" Version="3.7.104.21"/>
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.400.2"/>
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.7"/>
    <PackageReference Include="AWSSDK.Kinesis" Version="3.7.100.21"/>
      <PackageReference Include="AWSSDK.QuickSight" Version="3.7.403.1"/>
    <PackageReference Include="AWSSDK.S3" Version="3.7.8.13"/>
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.400.11" />
    <PackageReference Include="AWSSDK.SecurityToken" Version="3.7.400.18"/>
    <PackageReference Include="Consul" Version="0.7.2.6"/>
    <PackageReference Include="DeviceDetector.NET" Version="6.1.4"/>
    <PackageReference Include="FluentValidation" Version="11.9.1"/>
    <PackageReference Include="GoogleReCaptcha.V3" Version="1.3.1"/>
    <PackageReference Include="Grpc.AspNetCore" Version="2.70.0"/>
    <PackageReference Include="Grpc.AspNetCore.Server.Reflection" Version="2.70.0"/>
    <PackageReference Include="Grpc.Core.Api" Version="2.70.0"/>
    <PackageReference Include="Grpc.Tools" Version="2.71.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="HtmlAgilityPack" Version="1.11.53"/>
    <PackageReference Include="Hangfire" Version="1.8.5"/>
      <PackageReference Include="Hangfire.PostgreSql" Version="1.20.10"/>
    <PackageReference Include="Hangfire" Version="1.8.5"/>
    <PackageReference Include="Hangfire.PostgreSql" Version="1.20.3"/>
    <PackageReference Include="Jaeger" Version="1.0.3"/>
    <PackageReference Include="libphonenumber-csharp" Version="8.13.38"/>
    <PackageReference Include="MassTransit" Version="8.3.4"/>
    <PackageReference Include="MassTransit.Abstractions" Version="8.3.4"/>
    <PackageReference Include="MassTransit.AmazonSQS" Version="8.3.4"/>
    <PackageReference Include="MassTransit.Analyzers" Version="8.3.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
      <PackageReference Include="MassTransit.MessagePack" Version="8.3.4"/>
    <PackageReference Include="MediatR" Version="8.0.1"/>
    <PackageReference Include="Microsoft.AspNetCore" Version="2.2.0"/>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.19"/>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.0.0"/>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.0.0"/>
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>


      <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.0"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="6.0.1"/>
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="6.0.8"/>
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0"/>
      <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0"/>
      <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0"/>
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="2.3.2"/>
      <PackageReference Include="NewRelic.LogEnrichers.Serilog" Version="1.2.0"/>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
    <PackageReference Include="Newtonsoft.Json.Bson" Version="1.0.2"/>
    <PackageReference Include="Npgsql" Version="9.0.2"/>
    <PackageReference Include="Npgsql.DependencyInjection" Version="9.0.2"/>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2"/>
      <PackageReference Include="OpenTelemetry" Version="1.10.0"/>
      <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.10.0"/>
      <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.10.0"/>
      <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.10.0"/>

      <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0"/>
      <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.10.0"/>
      <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.9.0"/>
    <PackageReference Include="Polly" Version="7.2.1"/>
    <PackageReference Include="Portable.BouncyCastle" Version="1.9.0"/>
    <PackageReference Include="RabbitMQ.Client" Version="6.1.0"/>
    <PackageReference Include="RawRabbit" Version="2.0.0-rc5"/>
    <PackageReference Include="RawRabbit.Enrichers.MessageContext" Version="2.0.0-rc5"/>
    <PackageReference Include="RedLock.net" Version="2.3.2"/>
    <PackageReference Include="RestEase" Version="1.4.12" />
    <PackageReference Include="SendGrid" Version="9.22.0" />
      <PackageReference Include="Serilog" Version="3.1.1"/>
    <PackageReference Include="Serilog.AspNetCore" Version="5.0.0" />
    <PackageReference Include="Serilog.Enrichers.ClientInfo" Version="2.1.2"/>
      <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0"/>
    <PackageReference Include="Serilog.Sinks.AwsCloudWatch.Extensions" Version="3.0.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1"/>
      <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="9.0.3"/>
      <PackageReference Include="Serilog.Sinks.Seq" Version="6.0.0"/>
      <PackageReference Include="SSH.NET" Version="2024.1.0"/>
    <PackageReference Include="StackExchange.Redis" Version="2.6.80" />
    <PackageReference Include="SwaggerUi" Version="1.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.1.4" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="6.1.4" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.5.0" />
      <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="9.0.0"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="7.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="6.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="2.3.2" />
      <PackageReference Include="NewRelic.LogEnrichers.Serilog" Version="1.2.0"/>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Newtonsoft.Json.Bson" Version="1.0.2" />
      <PackageReference Include="OpenTelemetry" Version="1.6.0"/>
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.3.2" />
      <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.6.0"/>
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.0.0-rc9.4" />
      <PackageReference Include="Serilog" Version="3.1.1"/>
      <PackageReference Include="Serilog.Enrichers.ClientInfo" Version="2.0.3"/>
      <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0"/>
      <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1"/>
      <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="9.0.3"/>
      <PackageReference Include="Serilog.Sinks.Seq" Version="6.0.0"/>
      <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.0"/>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0"/>
    <PackageReference Include="TimeZoneConverter" Version="3.5.0" />
    <PackageReference Include="Twilio" Version="6.5.0" />
    <PackageReference Include="VaultSharp" Version="*******" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Jaeger\" />
      <Folder Include="Shared\Eligibility\Services\"/>
      <Folder Include="RedShift\"/>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.8.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.8.*" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\FlexCharge.Contracts\FlexCharge.Contracts.csproj" />
    <ProjectReference Include="..\FlexCharge.Utils\FlexCharge.Utils.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="MassTransit\DeferredBusStart\DeferredStartBusControl.cs"/>
  </ItemGroup>
  <ItemGroup>
    <None Remove="Shared\Payments\GRPC\PaymentProcessing.proto"/>
      <Protobuf Include="Shared\Orders\GRPC\Orders.proto">
          <GrpcServices>Both</GrpcServices>
          <Access>Public</Access>
          <ProtoCompile>True</ProtoCompile>
          <CompileOutputs>True</CompileOutputs>
          <OutputDir>obj/Debug/net9.0/</OutputDir>
          <Generator>MSBuild:Compile</Generator>
      </Protobuf>
      <Protobuf Include="Shared\Payments\BinChecker\GRPC\BinChecker.proto">
          <GrpcServices>Both</GrpcServices>
          <Access>Public</Access>
          <ProtoCompile>True</ProtoCompile>
          <CompileOutputs>True</CompileOutputs>
          <OutputDir>obj\Debug/net9.0/</OutputDir>
          <Generator>MSBuild:Compile</Generator>
      </Protobuf>
      <Protobuf Include="Shared\Tracking\GRPC\TrackingService.proto">
          <GrpcServices>Both</GrpcServices>
          <Access>Public</Access>
          <ProtoCompile>True</ProtoCompile>
          <CompileOutputs>True</CompileOutputs>
          <OutputDir>obj/Debug/net9.0/</OutputDir>
          <Generator>MSBuild:Compile</Generator>
      </Protobuf>
      <Protobuf Include="Shared\Vault\GRPC\VaultService.proto">
          <GrpcServices>Both</GrpcServices>
          <Access>Public</Access>
          <ProtoCompile>True</ProtoCompile>
          <CompileOutputs>True</CompileOutputs>
          <OutputDir>obj\Debug/net9.0/</OutputDir>
          <Generator>MSBuild:Compile</Generator>
      </Protobuf>
      <None Remove="Shared\Payments\GRPC\Payments.proto"/>
      <Protobuf Include="Shared\Payments\GRPC\Payments.proto"/>
  </ItemGroup>
</Project>