using System;
using System.Threading.Tasks;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Vault.Services;
using MassTransit;

namespace FlexCharge.Vault.Consumers;

public class DeTokenizeInstrumentCommandConsumer : IConsumer<DeTokenizeInstrumentCommand>
{
    private readonly IVaultService _vaultService;

    public DeTokenizeInstrumentCommandConsumer(IVaultService vaultService)
    {
        _vaultService = vaultService;
    }

    public async Task Consume(ConsumeContext<DeTokenizeInstrumentCommand> context)
    {
        using var workspan = Workspan.Start<DeTokenizeInstrumentCommandConsumer>()
            .Baggage("IsCit", context.Message.IsCit)
            .Baggage("Mid", context.Message.Mid)
            .Baggage("VaultKeyId", context.Message.VaultKeyId)
            .LogEnterAndExit();

        var message = context.Message;

        try
        {
            var detokenizedInstrument = await _vaultService.DeTokenize(
                message.VaultKeyId,
                message.Mid,
                message.IsCit,
                message.ECI,
                message.CAVV,
                amount: message.Amount,
                useLatestInstrument: message.GetLatestInstrument
            );

            await context.RespondAsync(new DeTokenizeInstrumentCommandResponse
            {
                Id = detokenizedInstrument.Id,
                CardHolderFirstName = detokenizedInstrument.CardHolderFirstName,
                CardHolderLastName = detokenizedInstrument.CardHolderLastName,
                ExpirationMonth = detokenizedInstrument.ExpirationMonth,
                Number = detokenizedInstrument.Number,
                VerificationValue = detokenizedInstrument.VerificationValue,
                ExpirationYear = detokenizedInstrument.ExpirationYear,
                CardNumberMasked = detokenizedInstrument.CardNumberMasked,
                Bin = detokenizedInstrument.Bin,
                Last4 = detokenizedInstrument.Last4,
                CardBrand = detokenizedInstrument.CardBrand,
                Email = detokenizedInstrument.Email,
                PhoneNumber = detokenizedInstrument.PhoneNumber,
                SenseKey = detokenizedInstrument.SenseKey,
                BillingAddress = detokenizedInstrument.BillingAddress,
                ShippingAddress = detokenizedInstrument.ShippingAddres,
                ValidLuhn = detokenizedInstrument.ValidLuhn,
                CvvInputValidationResult = detokenizedInstrument.CvvInputValidationResult,

                NetworkTokenInfo = detokenizedInstrument.NetworkTokenInfo,
                AccountLastUpdatedAt = detokenizedInstrument.AccountUpdatesCount > 0
                    ? DateTime.UtcNow
                    : null,
                ReturnedFromAccountUpdaterOn = detokenizedInstrument.ReturnedFromAccountUpdaterOn,
                AccountUpdaterResponseReceivedOn = detokenizedInstrument.AccountUpdaterResponseReceivedOn
            });
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            throw;
        }
    }
}