using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Amazon;
using Amazon.KeyManagementService;
using Amazon.KeyManagementService.Model;
using Amazon.Runtime;
using CreditCardValidator;
using EntityFramework.Exceptions.Common;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Exceptions;
using FlexCharge.Common.Logging.LogSuppression;
using FlexCharge.Common.Telemetry;
using FlexCharge.Contracts.Commands.Vault;
using FlexCharge.Contracts.Common;
using FlexCharge.Contracts.Vault;
using FlexCharge.Payments.BinChecker.CardBrands;
using FlexCharge.Utils;
using FlexCharge.Vault.Activities;
using FlexCharge.Vault.DTO;
using FlexCharge.Vault.Entities;
using FlexCharge.Vault.Services.NetworkTokenizationervice;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using static FlexCharge.Vault.Encryptor;


namespace FlexCharge.Vault.Services;

public class VaultService : IVaultService, IDisposable
{
    private PostgreSQLDbContext _localScopedDbContext;
    private IServiceScopeFactory _serviceFactory;
    private readonly IPublishEndpoint _publisher;
    private readonly IActivityService _activityService;
    private readonly INetworkTokenizationService _networkTokenizationService;
    private readonly ICardBrandDetector _cardBrandDetector;
    private readonly IServiceScope _serviceScope;

    public VaultService(IServiceScopeFactory serviceProvider, IPublishEndpoint publisher,
        IActivityService activityService, INetworkTokenizationService networkTokenizationService,
        ICardBrandDetector cardBrandDetector)
    {
        _serviceFactory = serviceProvider;
        _publisher = publisher;
        _activityService = activityService;

        _networkTokenizationService = networkTokenizationService;
        _cardBrandDetector = cardBrandDetector;

        _serviceScope = serviceProvider.CreateScope();

        // Local scoped DbContext is used to allow detaching duplicate vaults in AddOrGetVault method
        // without affecting the main DbContext
        _localScopedDbContext = _serviceScope.ServiceProvider.GetRequiredService<PostgreSQLDbContext>();
    }

    public void Dispose()
    {
        _serviceScope?.Dispose();
    }

    // public async Task<string> EncryptCC(string dataToEncrypt)
    // {
    //     using var workspan = Workspan.Start<VaultService>().LogEnterAndExit();
    //
    //     var enc = new AmazonKeyManagementServiceClient();
    //
    //     var response = await enc.EncryptAsync(new EncryptRequest()
    //     {
    //         Plaintext = GenerateStreamFromString(dataToEncrypt),
    //         KeyId = "arn:aws:kms:us-east-1:************:key/61285453-04b5-4f76-b79b-da3a8fe134d5",
    //     });
    //
    //     var base64String = Convert.ToBase64String(response.CiphertextBlob.ToArray());
    //     return base64String;
    // }

    public async Task<string> Decrypt(Guid token)
    {
        using var workspan = Workspan.Start<VaultService>().LogEnterAndExit();


        var vaultData = await _localScopedDbContext.VaultsV2.Where(x => x.Id == token).SingleOrDefaultAsync();
        var enc = new AmazonKeyManagementServiceClient(new BasicAWSCredentials(
                Environment.GetEnvironmentVariable("AWS_IAM_VAULT_KEY"),
                Environment.GetEnvironmentVariable("AWS_IAM_VAULT_SECRET")),
            RegionEndpoint.USEast1);

        if (vaultData == null) return null;

        var decryptedString = string.Empty;
        var response = await enc.DecryptAsync(new DecryptRequest
        {
            KeyId = "arn:aws:kms:us-east-1:************:key/61285453-04b5-4f76-b79b-da3a8fe134d5",
        });

        var base64String = Convert.ToBase64String(response.Plaintext.ToArray());
        return Utils.EncoderDecoder.Base64Decode(base64String);
    }

    public async Task<string> DecryptPciData(string data)
    {
        using var workspan = Workspan.Start<VaultService>().LogEnterAndExit();

        try
        {
            var decryptedString = string.Empty;
            var stringForDecryption = Convert.FromBase64String(data);
            var kmsClient = new AmazonKeyManagementServiceClient();

            var ciphertext = new MemoryStream();
            ciphertext.Write(stringForDecryption, 0, stringForDecryption.Length);

            var decryptRequest = new DecryptRequest()
            {
                KeyId = "arn:aws:kms:us-east-1:************:key/61285453-04b5-4f76-b79b-da3a8fe134d5",
                CiphertextBlob = ciphertext
            };
            var plainText = (await kmsClient.DecryptAsync(decryptRequest)).Plaintext;

            decryptedString = Encoding.UTF8.GetString(plainText.ToArray()) ?? string.Empty;
            return decryptedString;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: DecryptPciData");
            //throw;
            return null;
        }

        workspan.Log.Information("EXIT: DecryptCC");
        return null;
    }


    public async Task<Entities.VaultV2> AddOrGetVault(string number, int expirationMonth, int expirationYear)
    {
        using var workspan = Workspan.Start<VaultService>();

        // Non-PCI data
        string bin = null;
        string last4 = null;
        string cardNumberMasked = null;
        string fingerprint = null;

        VaultV2 vault = null;

        try
        {
            // We expect UniqueConstraintException to be thrown here -> turning error into warning in the log
            using var _ = DatabaseLogSuppressor
                .SuppressUniqueConstraintError<PostgreSQLDbContext>("VaultsV2",
                    "FingerPrint_CardNumberMasked_ExpirationYear_ExpirationMonth_Idx");

            var strippedCard = number.GetNumbers();


            bin = strippedCard.GetFirst(8);
            last4 = strippedCard.GetLast(4);

            workspan
                .Baggage("Bin", bin)
                .Baggage("Last4", last4);

            cardNumberMasked = strippedCard.Mask(6, strippedCard.Length - 10, '*');
            fingerprint = Utils.CryptoSigningHelper.ComputeContentHash(strippedCard);

            vault = new Entities.VaultV2
            {
                Number = null,
                Token = await EncryptCC(strippedCard),
                Fingerprint = fingerprint,
                ExpirationMonth = expirationMonth,
                ExpirationYear = expirationYear,
                CardNumberMasked = cardNumberMasked,
                Bin = bin,
                Last4 = last4,
                ValidLuhn = IsCardValid(number)
            };

            await _localScopedDbContext.AddAsync(vault);
            var res = await _localScopedDbContext.SaveChangesAsync();

            if (res == 0) throw new Exception("Unable to store tokenized card");
        }
        catch (UniqueConstraintException e) when (vault != null && e.Entries.Count == 1 && e.Entries[0].Entity == vault)
        {
            vault = await GetAndUpdateLastUsed(vault);
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(VaultErrorActivities.Vault_Tokenization_Error,
                set: set => set
                    .CorrelationId(vault?.Id)
                    .Meta(meta => meta
                        .SetValue("Bin", bin)
                        .SetValue("Last4", last4)
                    )
            );

            return null;
        }

        return vault;
    }


    bool IsCardValid(string number) => new CreditCardDetector(number).IsValid();

    public CvvInputValidationResult? CvvPassingInputValidation(string cvv)
    {
        switch (cvv)
        {
            case null:
                return CvvInputValidationResult.Null;
            case "":
                return CvvInputValidationResult.Empty;
            default:
                return Regex.IsMatch(cvv, "^[0-9]{3,4}$")
                    ? CvvInputValidationResult.Passed
                    : CvvInputValidationResult.NotPassed;
        }
    }

    public async Task<CreditCardQueryDTO> Tokenize(Guid? mid, CreditCardDTO card,
        bool enabledGlobalNetworkTokenization = false)
    {
        using var workspan = Workspan.Start<VaultService>()
            .Baggage("Mid", mid)
            .LogEnterAndExit();

        string bin = null;
        string last4 = null;


        VaultV2 vaultV2 = null;
        Guid vaultV2Id = Guid.Empty;
        var paymentInstrumentId = Guid.NewGuid();

        var strippedCard = card.Number.GetNumbers();

        CreditCardQueryDTO storedCreditCardInfo = null;

        string fingerprint = null;
        string cardNumberMasked = null;
        try
        {
            workspan.Log.Information("Storing card in vault");

            // We expect UniqueConstraintException to be thrown here -> turning error into warning in the log
            using var _ = DatabaseLogSuppressor
                .SuppressUniqueConstraintError<PostgreSQLDbContext>("VaultsV2",
                    "FingerPrint_CardNumberMasked_ExpirationYear_ExpirationMonth_Idx");

            // encryptedCvv = !string.IsNullOrEmpty(card.VerificationValue)
            //     ? await EncryptCVV(cvvNumbers)
            //     : null;

            fingerprint = CryptoSigningHelper.ComputeContentHash(strippedCard);
            var maskLength = strippedCard.Length - 10;

            cardNumberMasked = maskLength > 0
                ? strippedCard.Mask(6, maskLength, '*')
                : "*";

            bin = strippedCard.GetFirst(8);
            last4 = strippedCard.GetLast(4);

            workspan
                .Baggage("Bin", bin)
                .Baggage("Last4", last4);

            await _localScopedDbContext.SaveChangesAsync();

            vaultV2 = new VaultV2
            {
                Number = null,
                Token = await EncryptCC(strippedCard),
                Fingerprint = fingerprint,
                ExpirationMonth = card.ExpirationMonth,
                ExpirationYear = card.ExpirationYear,
                CardNumberMasked = cardNumberMasked,
                Bin = bin,
                Last4 = last4,
                ValidLuhn = IsCardValid(card.Number),
            };

            var tokenizedCard = await _localScopedDbContext.VaultsV2.AddAsync(vaultV2);
            vaultV2Id = tokenizedCard.Entity.Id;

            storedCreditCardInfo = new CreditCardQueryDTO
            {
                Mid = mid,
                // Id = tokenizedCard.Entity.Id,-sets later

                ValidLuhn = vaultV2.ValidLuhn,

                CardHolderFirstName = card.CardHolderFirstName?.Trim(),
                CardHolderLastName = card.CardHolderLastName?.Trim(),
                ExpirationMonth = tokenizedCard.Entity.ExpirationMonth,
                ExpirationYear = tokenizedCard.Entity.ExpirationYear,
                CardNumberMasked = cardNumberMasked,
                Fingerprint = fingerprint,
                Bin = tokenizedCard.Entity.Bin,
                Last4 = tokenizedCard.Entity.Last4,
                BillingAddress = card.BillingAddress,
                ShippingAddres = card.ShippingAddress,
                SenseKey = card.SenseKey,
                Email = card.Email,
                PhoneNumber = card.PhoneNumber
            };

            var res = await _localScopedDbContext.SaveChangesAsync();
            if (res == 0) throw new Exception("Unable to store tokenized card");
        }
        catch (UniqueConstraintException e) when (vaultV2 != null && e.Entries.Count == 1 &&
                                                  e.Entries[0].Entity == vaultV2)
        {
            vaultV2 = await GetAndUpdateLastUsed(vaultV2);
            vaultV2Id = vaultV2.Id;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(VaultErrorActivities.Vault_Tokenization_CreateVault_Error,
                set: set => set
                    .TenantId(mid)
                    .Meta(meta => meta
                        .SetValue("Bin", bin)
                        .SetValue("Last4", last4)
                    )
            );
            return null;
        }

        try
        {
            workspan.Log.Information("Creating payment instrument");

            //var cvvNumbers = card.VerificationValue.GetNumbers();
            var cvvPassedInputValidation = CvvPassingInputValidation(card.VerificationValue);
            var encryptedCvv = !string.IsNullOrEmpty(card.VerificationValue)
                ? await EncryptCVV(card.VerificationValue)
                : null;

            PaymentInstrument paymentInstrument = new()
            {
                Id = paymentInstrumentId,
                VaultV2Id = vaultV2Id,
                Mid = mid,

                CardHolderFirstName = card.CardHolderFirstName?.Trim(),
                CardHolderLastName = card.CardHolderLastName?.Trim(),
                CardHolderFirstNameNormalized = card.CardHolderFirstName?.Trim().ToUpper(),
                CardHolderLastNameNormalized = card.CardHolderLastName?.Trim().ToUpper(),
                CardHolderName = $"{card.CardHolderFirstName?.Trim()} {card.CardHolderLastName?.Trim()}",

                Cvv = encryptedCvv,
                CvvInputValidationResult = cvvPassedInputValidation.ToString(),
                BillingAddress = card.BillingAddress,
                ShippingAddress = card.ShippingAddress,
                Email = card.Email,
                SenseKey = card.SenseKey
            };

            await _localScopedDbContext.PaymentInstruments.AddAsync(paymentInstrument);
            await _localScopedDbContext.SaveChangesAsync();

            storedCreditCardInfo.Id = paymentInstrument.Id;
            storedCreditCardInfo.VaultId = vaultV2Id;
            storedCreditCardInfo.CvvInputValidationResult = paymentInstrument.CvvInputValidationResult;

#if DEBUG
            enabledGlobalNetworkTokenization = true;
#endif

            if (enabledGlobalNetworkTokenization)
            {
                workspan.Log.Information("Requesting network tokenization");

                var ntEevent = new NetworkTokenRequestedEvent()
                {
                    PaymentInstrumentToken = paymentInstrumentId,
                    SenseKey = card.SenseKey,
                    Mid = mid,
                    BillingAddress = card.BillingAddress,
                };
                await _publisher.Publish(ntEevent);
                workspan.Log.Information("Network Token {@Event} published", ntEevent);
            }

            return storedCreditCardInfo;
        }
        catch (Exception e)
        {
            workspan.RecordException(e);
            await _activityService.CreateActivityAsync(
                VaultErrorActivities.Vault_Tokenization_CreatePaymentInstrument_Error,
                set: set => set
                    .TenantId(mid)
                    .Meta(meta => meta
                        .SetValue("Bin", bin)
                        .SetValue("Last4", last4)
                    )
            );

            return null;
        }
    }

    class UpdatedVaultIdSearchResult
    {
        public VaultV2 Vault { get; }
        public int AccountUpdatesCount { get; }
        //   public FlexAccountUpdaterMessage? FlexAccountUpdateMessage { get; set; }

        public UpdatedVaultIdSearchResult(VaultV2 vault, int accountUpdatesCount)
        {
            Vault = vault;
            AccountUpdatesCount = accountUpdatesCount;
            //FlexAccountUpdateMessage = flexAccountUpdateMessage;
        }
    }

    private async Task<UpdatedVaultIdSearchResult> GetLastVaultIdWithAccountUpdaterAsync(Entities.VaultV2 vault,
        bool useaccountUpdater = false)
    {
        using var workspan = Workspan.Start<VaultService>()
            .Tag("CurrentVaultId", vault.Id)
            .Tag("CurrentBin", vault.Fingerprint)
            .Tag("CurrentLast4", vault.Fingerprint);

        workspan.Log
            .Information("Looking for updated vault");

        Dictionary<Guid, VaultV2> vaults = new() {{vault.Id, vault}};
        var initialVaultId = vault.Id;

        // auSettings = auSettings ?? new();
        FlexAccountUpdaterMessage? accountUpdateMessage = null;

        while (vault is not null)
        {
            if (!useaccountUpdater)
            {
                workspan.Log
                    .Information("Account updater permissions disabled update");

                break;
            }

            if (!vault.NextVaultId.HasValue)
                break;


            var vaultId = vault.NextVaultId.Value;

            vault =
                await _localScopedDbContext.VaultsV2.SingleOrDefaultAsync(v => v.Id == vaultId);

            if (vault is null)
            {
                workspan.Log
                    .Fatal("NEXT VAULT {NextVaultId} NOT FOUND", vaultId);

                break;
            }

            if (!vaults.TryAdd(vault.Id, vault))
            {
                workspan
                    .Tag("NextVaultId", vault.Id)
                    .Log.Fatal("INFINITE LOOP FOUND: Updated vault already processed");

                break;
            }
        }


        var result = vaults.Values
            .OrderByDescending(v => v.NextVaultId == null)
            .ThenByDescending(v => v.CreatedOn)
            .FirstOrDefault();


        if (vaults.Count > 1)
        {
            workspan.Log
                .Information("Found updated vault {UpdatedVaultId} for vault {InitialVaultId}", result.Id,
                    initialVaultId);
        }

        return new UpdatedVaultIdSearchResult(result, vaults.Count);
    }


    public async Task<CreditCardQueryDTO> DeTokenize(
        Guid paymentInstrumentToken,
        Guid mid,
        bool isCit,
        string ECI = null,
        string cavv = null,
        bool useLatestInstrument = false,
        int amount = 0)
    {
        using var workspan = Workspan.Start<VaultService>().LogEnterAndExit();

        // auSettings = auSettings ?? new();

        async Task<NetworkTokenInfo> GetTokenAndCryptogram(string cardNumber)
        {
            using var workspan = Workspan.Start<NetworkTokenInfo>().LogEnterAndExit();

            NetworkTokenInfo result = null;
            try
            {
                result = await _networkTokenizationService
                    .GetTokenAndCryptogramResult(paymentInstrumentToken, cardNumber,
                        amount, isCit, ECI, cavv);

                if (result is not null)
                {
                    workspan.Log.Information(" GetTokenAndCrypto > Result");
                }
                else
                {
                    workspan.Log.Information(" GetTokenAndCrypto > NO Result");
                }
            }

            catch (Exception e)
            {
                workspan.RecordException(e);
            }
#if DEBUG
            if (result is not null) result.Eci = "05";
#endif

            return result;
        }

        try
        {
            var payment = await _localScopedDbContext.PaymentInstruments
                .SingleOrDefaultAsync(x => x.Id == paymentInstrumentToken && x.Mid == mid);


            var result = payment?.VaultV2;
            if (result is null)
            {
                throw new FlexChargeException("vault not found");
            }

            var vaultInfo = await GetLastVaultIdWithAccountUpdaterAsync(result, useLatestInstrument);

            var vault = vaultInfo?.Vault;

            if (vault is null)
                throw new FlexChargeException("vault not found");

            var isAccountUpdated = vault.Id != result.Id;
            if (isAccountUpdated)
            {
                workspan.Log.Information(
                    "ACCOUNT UPDATER: DeTokenize returned for {OldCardId} updated card ID {NewCardId}", result.Id,
                    vault.Id);

                result = vault;

                payment.AccountUpdaterVaultV2Ids = payment.AccountUpdaterVaultV2Ids is null
                    ? new() {vault.Id}
                    : payment.AccountUpdaterVaultV2Ids.Append(vault.Id).ToList();

                //update reference to payment instrument
                payment.VaultV2 = vault; //move pointer to the new vaultId
            }

            vault.LastUsed = DateTime.UtcNow;
            await _localScopedDbContext.SaveChangesAsync();

            if (isAccountUpdated)
            {
                await _activityService.CreateActivityAsync(
                    AccountUpdaterActivities.AccountUpdater_PaymentInstrumentUpdatedSuccessfully,
                    set: set => set
                        .TenantId(payment.Mid)
                        .CorrelationId(payment.Id)
                        .Meta(meta => meta
                            .SetValue("OldCardId", result.Id)
                            .SetValue("NewCardId", vault.Id)
                        ));
            }


            var cardNumber = await DecryptPciData(result.Token);
            var cvv = string.IsNullOrWhiteSpace(payment.Cvv) ? null : await DecryptPciData(payment.Cvv);


            var tokenInfo = await GetTokenAndCryptogram(cardNumber);

            var brand = _cardBrandDetector.GetBrand(cardNumber);

            var creditCardQueryDto = new CreditCardQueryDTO
            {
                Id = paymentInstrumentToken, //result.Id,
                CardHolderFirstName = payment.CardHolderFirstName,
                CardHolderLastName = payment.CardHolderLastName,
                Number = cardNumber,
                VerificationValue = isCit ? cvv : null,
                ExpirationMonth = result.ExpirationMonth,
                ExpirationYear = result.ExpirationYear,
                CardNumberMasked = result.CardNumberMasked,
                Bin = result.Bin,
                Last4 = result.Last4,
                IsAccountUpdaterActive = result.IsAccountUpdaterActive,
                AccountUpdaterResultPending = result.PendingUpdate,
                FlexAccountUpdaterMessage = vault.FlexAccountUpdaterMessage,
                AccountUpdaterExpiry = result.AccountUpdateRelevantBefore,
                AccountUpdatesCount = payment.AccountUpdaterVaultV2Ids?.Count ?? 0, // vaultInfo.AccountUpdatesCount,
                BillingAddress = payment.BillingAddress,
                ShippingAddres = payment.ShippingAddress,
                SenseKey = payment.SenseKey,
                Email = payment.Email,
                PhoneNumber = payment.Phone,
                ValidLuhn = result.ValidLuhn,
                CvvInputValidationResult = payment.CvvInputValidationResult,
                NetworkTokenInfo = tokenInfo,
                CardBrand = brand,
                ReturnedFromAccountUpdaterOn = vault.ReturnedFromAccountUpdaterOn,
                AccountUpdaterResponseReceivedOn = vault.AccountUpdaterResponseReceivedOn
            };

            workspan
                .Baggage("CvvPresent", !string.IsNullOrWhiteSpace(creditCardQueryDto.VerificationValue));

            return creditCardQueryDto;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: DeTokenize");
            return null;
        }
    }

    public async Task<CreditCardQueryDTO> GetVault(Guid paymentInstrumentToken, Guid mid,
        bool useAccountUpdater = false)
    {
        using var workspan = Workspan.Start<CreditCardQueryDTO>()
            .Baggage("PaymentInstrumentToken", paymentInstrumentToken)
            .Baggage("Mid", mid);


        //  auSettings = auSettings ?? new();

        try
        {
            var paymentInstrument = await _localScopedDbContext.PaymentInstruments
                //.Include(p=>p.AccountUpdaterVaultV2Ids)
                .SingleOrDefaultAsync(x => x.Id == paymentInstrumentToken);

            if (paymentInstrument is null)
            {
                return new()
                {
                    TokenIsDeleted = true
                };
            }

            if (paymentInstrument.Mid == null)
            {
                workspan.Log
                    .Error("PaymentInstrument does not have a Mid");
            }
            else if (paymentInstrument.Mid != mid)
            {
                workspan.Log
                    .Fatal("PaymentInstrument does not belong to this merchant");

                paymentInstrument = null;
            }

            var activeVault = paymentInstrument?.VaultV2;

            if (activeVault is null)
                throw new FlexChargeException("vault not found");

            var vaultInfo = await GetLastVaultIdWithAccountUpdaterAsync(activeVault, useAccountUpdater);

            activeVault = vaultInfo.Vault;

            return new CreditCardQueryDTO
            {
                Id = paymentInstrumentToken, //result.Id,
                CardHolderFirstName = paymentInstrument.CardHolderFirstName,
                CardHolderLastName = paymentInstrument.CardHolderLastName,
                Number = activeVault.Token,
                Fingerprint = activeVault.Fingerprint,
                ////VerificationValue = cvv, // Get Vault does not return PCI data
                ExpirationMonth = activeVault.ExpirationMonth,
                ExpirationYear = activeVault.ExpirationYear,
                CardNumberMasked = activeVault.CardNumberMasked,

                IsAccountUpdaterActive = activeVault.IsAccountUpdaterActive,

                Bin = activeVault.Bin,
                Last4 = activeVault.Last4,

                AccountUpdaterResultPending = activeVault.PendingUpdate,
                FlexAccountUpdaterMessage = activeVault.FlexAccountUpdaterMessage,
                AccountUpdaterExpiry = activeVault.AccountUpdateRelevantBefore,
                AccountUpdatesCount =
                    paymentInstrument.AccountUpdaterVaultV2Ids?.Count ?? 0, // vaultInfo.AccountUpdatesCount,
                BillingAddress = paymentInstrument.BillingAddress,
                ShippingAddres = paymentInstrument.ShippingAddress,
                Email = paymentInstrument.Email,
                SenseKey = paymentInstrument.SenseKey,
                PhoneNumber = paymentInstrument.Phone,

                ValidLuhn = activeVault.ValidLuhn,
                CvvInputValidationResult = paymentInstrument.CvvInputValidationResult,

                ReturnedFromAccountUpdaterOn = activeVault.ReturnedFromAccountUpdaterOn,
                AccountUpdaterResponseReceivedOn = activeVault.AccountUpdaterResponseReceivedOn
            };
        }
        catch (Exception e)
        {
            workspan.RecordFatalException(e, "DeTokenize");
            return null;
        }
    }


    public async Task<bool> RecacheVerificationValue(Guid paymentInstrumentToken, string value)
    {
        using var workspan = Workspan.Start<VaultService>();
        workspan.Log.Information("ENTERED: RecacheVerificationValue");


        try
        {
            var paymentInstrument = await _localScopedDbContext.PaymentInstruments
                .SingleOrDefaultAsync(x => x.Id == paymentInstrumentToken);
            var result = paymentInstrument?.VaultV2;

            if (result is null)
                throw new NullReferenceException("RecacheVerificationValue: vault not found");

            paymentInstrument.Cvv = await EncryptCC(value.GetNumbers());

            _localScopedDbContext.PaymentInstruments.Update(paymentInstrument);
            var res = await _localScopedDbContext.SaveChangesAsync();

            return res > 0;
        }
        catch (Exception e)
        {
            workspan.Log.Error(e, "EXCEPTION: RecacheVerificationValue");
            return false;
        }
    }

    private static MemoryStream GenerateStreamFromString(string s)
    {
        var stream = new MemoryStream();
        var writer = new StreamWriter(stream);
        writer.Write(s);
        writer.Flush();
        stream.Position = 0;
        return stream;
    }

    private async Task<VaultV2> GetAndUpdateLastUsed(VaultV2 vault, HashSet<Guid> guardVaultIds = null)
    {
        guardVaultIds = guardVaultIds ?? new() {vault.Id};


        VaultV2 result = vault;
        _localScopedDbContext.Entry(vault).State = EntityState.Detached;

        ///try to update lastUsed if card is not deleted
        int updatedRows = await _localScopedDbContext.VaultsV2
            .Where(v => v.Fingerprint == vault.Fingerprint
                        && v.CardNumberMasked == vault.CardNumberMasked
                        && v.ExpirationMonth == vault.ExpirationMonth
                        && v.ExpirationYear == vault.ExpirationYear
                // && !v.IsDeleted
            )
            .ExecuteUpdateAsync(settings => settings
                .SetProperty(a => a.LastUsed, DateTime.UtcNow));


        result = await _localScopedDbContext.VaultsV2
            .IgnoreQueryFilters() //deleted card can have not deleted nextValue from AU, we want to update this one also
            .OrderBy(v => v.IsDeleted) // this ensures that not deleted vaults are first
            .ThenByDescending(v => v.CreatedOn)
            .FirstAsync(v => v.Fingerprint == vault.Fingerprint
                             && v.CardNumberMasked == vault.CardNumberMasked
                             && v.ExpirationMonth == vault.ExpirationMonth
                             && v.ExpirationYear == vault.ExpirationYear);

        if (updatedRows == 0) //no rows updated, it means item was deleted, add card again
        {
            vault.NextVaultId = result.NextVaultId;
            vault.AccountUpdaterEnabled = result.AccountUpdaterEnabled;
            vault.AccountUpdateRelevantBefore = result.AccountUpdateRelevantBefore;
            vault.IsAccountUpdaterActive = result.IsAccountUpdaterActive;
            vault.PendingUpdate = result.PendingUpdate;
            vault.FlexAccountUpdaterMessage = result.FlexAccountUpdaterMessage;
            vault.FlexAccountUpdaterMessage = result.FlexAccountUpdaterMessage;
            vault.AccountUpdaterMessage = result.AccountUpdaterMessage;


            await _localScopedDbContext.AddAsync(vault);

            result = vault;

            var res = await _localScopedDbContext.SaveChangesAsync();
            if (res == 0) throw new Exception("Unable to update lastUsed in tokenized card");
        }


        if (result.NextVaultId.HasValue && !guardVaultIds.Add(result.NextVaultId.Value))
        {
            var nextVault = await _localScopedDbContext.VaultsV2
                .SingleOrDefaultAsync(v => v.Id == result.NextVaultId.Value);

            if (nextVault is not null)
            {
                await GetAndUpdateLastUsed(nextVault, guardVaultIds);
            }
            else // autoUpdated vault is deleted
            {
                result.NextVaultId = null;
                // result.FlexAccountUpdaterMessage = null;//todo-uncomment or not?
                // result.AccountUpdaterMessage = null;//todo
                await _localScopedDbContext.SaveChangesAsync();
            }
        }

        return result.IsDeleted ? null : result;
    }
}