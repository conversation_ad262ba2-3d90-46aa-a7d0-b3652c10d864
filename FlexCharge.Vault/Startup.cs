using FlexCharge.Common;
using FlexCharge.Common.Authentication;
using FlexCharge.Common.PostgreSql;
using FlexCharge.Common.Swagger;
using MassTransit;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System;
using FlexCharge.Common.BackgroundJobs;
using FlexCharge.Common.Cloud.Storage;
using FlexCharge.Common.Encryption;
using System.Threading;
using Amazon.S3;
using FlexCharge.Common.Activities;
using FlexCharge.Common.Cache;
using FlexCharge.Common.Grpc;
using FlexCharge.Common.MassTransit;
using FlexCharge.Common.SftpServices;
using FlexCharge.Common.Telemetry;
using FlexCharge.Common.Telemetry.PerformanceCounters;
using FlexCharge.Payments.BinChecker.CardBrands;
using FlexCharge.Vault.HostedServices;
using FlexCharge.Vault.Services;
using FlexCharge.Vault.Services.AccountUpdaterServices;
using FlexCharge.Vault.Services.NetworkTokenizationervice;
using Polly;


namespace FlexCharge.Vault
{
    public class Startup
    {
        public static EventWaitHandle StartupCompleted { get; } = new(false, EventResetMode.ManualReset);

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTelemetry();
            services.AddCloudWatchPerformanceCountersTelemetry<Startup>();
            services.AddSingleton<ICardBrandDetector, CardBrandDetector>();

            //Move to a separate project (AccountUpdater microservice)
            services.AddTransient<ISftpClientService, SftpClientService>();
            services.AddTransient<IPgpEncryptionService, PgpEncryptionService>();
            //  services.AddTransient<IBulkAccountUpdaterService, TokenExAccountUpdaterService>();
            //services.AddTransient<ITokenExNetworkTokenizationService, TokenExNetworkTokenizationService>();

            services.AddTransient<INetworkTokenizationService, NetworkTokenizationTokenexService>();
            services.AddHttpClient<TokenExSDK>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5))
                .AddTransientHttpErrorPolicy(policyBuilder =>
                    policyBuilder.WaitAndRetryAsync(
                        3, retryNumber => TimeSpan.FromMilliseconds(600)));

            services.AddTransient<IAccountUpdaterService, TokenExAccountUpdaterService>();
            services.AddTransient<ICloudStorage, AmazonS3Storage>();

            services.AddActivities();
            services.AddAWSService<IAmazonS3>();
            //Move to a separate project

            services.AddTransient<IVaultService, VaultService>();
            services.AddTransient<IHttpContextAccessor, HttpContextAccessor>();
            services.AddTransient(typeof(IOptions<>), typeof(OptionsManager<>));

            services.Configure<AppOptions>(Configuration.GetSection("app"));
            services.Configure<NetworkTokenOptions>(Configuration.GetSection(NetworkTokenOptions.Section));
            services.AddOptions();

            services.AddHostedService<OlderThen365DaysVaultRemovalService>();
            services.AddHostedService<DeleteCvvFromVault>();
            services.AddHostedService<DB_LiveCreditCard_DetectionService>();

            services.AddScoped<IRealTimeAccountUpdaterService, RealTimeAccountUpdaterService>();

            var connectionString =
                $@"Host={Environment.GetEnvironmentVariable("DB_HOST")};Port={Environment.GetEnvironmentVariable("DB_PORT")};Database={Environment.GetEnvironmentVariable("DB_DATABASE")};Username={Environment.GetEnvironmentVariable("DB_USERNAME")};Password='{Environment.GetEnvironmentVariable("DB_PASSWORD")}';";
#if DEBUG
            connectionString = "Host=localhost;Database=fc.vault;Username=vault-service-staging;Password=*****";
#endif

            services.AddEntityFrameworkNpgsql()
                .AddNpgsqlDbContext<PostgreSQLDbContext>(connectionString);
            //.AddNpgsqlDbContext<PostgreSQLDbContext>("Host=localhost;Database=fc.vaults;Username=vault-service-staging;Password=*****");

            services.AddJwt();

            // services.AddSingleton<IAuthorizationHandler, ContactAuthorizationHandler>();
            // services.AddSingleton<IAuthorizationHandler, ContactAuthorizationCrudHandler>();
            // services.AddAuthorization(options =>
            //     {
            //         options.AddPolicy(MyPolicies.ADMINS_ONLY,
            //             policy => policy.RequireClaim(SuperAdminGroups.MERCHANT_ADMIN));
            //         options.AddPolicy(MyPolicies.USERS,
            //             policy => policy.RequireClaim(SuperAdminGroups.USER));
            //
            //         options.AddPolicy(MyPolicies.READ, policy =>
            //             policy.Requirements.Add(new IsOwnerRequirement()));
            //     }
            // );

            services.AddAutoMapper(typeof(Startup));
            services.AddControllers();
            services.AddSwaggerDocs();


            services.AddMassTransit<Startup>(
                x => { });

            #region GRPC

            services.AddFlexGrpc();

            #endregion

            services.AddRedisCache();

            services.AddBackgroundWorkerService(Configuration);

            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy", cors =>
                    cors.AllowAnyMethod()
                        .AllowAnyOrigin()
                        .AllowAnyHeader());
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IHostApplicationLifetime applicationLifetime,
            IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                // Enable middleware to serve generated Swagger as a JSON endpoint.
            }

            app.UseCors("CorsPolicy");

            //app.UseHttpsRedirection();
            app.UseSwaggerDocs();

            app.UseRouting();
            app.UseAuthorization();

            app.UseAutoMigrations<PostgreSQLDbContext>();
            app.UseEndpoints(endpoints => { endpoints.MapControllers(); });

            #region GRPC

            // app.UseGrpcEndpoints(endpoints =>
            //     endpoints
            //         .Map<GrpcVaultService>()
            // );

            #endregion

            app.UseMassTransit();

            StartupCompleted.Set();
        }
    }
}