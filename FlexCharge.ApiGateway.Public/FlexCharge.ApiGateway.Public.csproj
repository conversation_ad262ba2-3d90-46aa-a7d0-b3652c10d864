<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
		<UserSecretsId>99bbd081-7f55-4d77-9823-a712a2c8e7e0</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<!--<DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>-->
		<Configurations>Debug;Release;Staging</Configurations>
		<WarningsAsErrors>CS4014</WarningsAsErrors>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>bin\$(Configuration)\$(AssemblyName).xml</DocumentationFile>
		<NoWarn>1701;1702;1591;</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="MediatR" Version="8.0.1" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.8" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FlexCharge.Common\FlexCharge.Common.csproj" />
		<ProjectReference Include="..\FlexCharge.Contracts\FlexCharge.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Include="..\FlexCharge.ApiClient\DTO\ApiKeysResult.cs">
	    <Link>DTO\ApiKeysResult.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.ApiClient\DTO\ApiKeyVerify.cs">
	    <Link>DTO\ApiKeyVerify.cs</Link>
	  </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\DTO\EvaluateRequest.cs">
            <Link>DTO\TransmitAndEvaluate\EvaluateRequest.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\DTO\SharedDTOs.cs">
            <Link>DTO\TransmitAndEvaluate\SharedDTOs.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\DTO\ThreeDsecure.cs">
            <Link>DTO\TransmitAndEvaluate\ThreeDsecure.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\DTO\TransmitAndEvaluateRequestBase.cs">
            <Link>DTO\TransmitAndEvaluate\TransmitAndEvaluateRequestBase.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\BlockableFingerprintAttribute.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\BlockableFingerprintAttribute.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\Fingerprint.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\Fingerprint.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\FingerprintAttribute.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\FingerprintAttribute.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\FingerprintAttributeBase.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\FingerprintAttributeBase.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\FingerprintBuilder.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\FingerprintBuilder.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\FingerprintCategory.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\FingerprintCategory.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\FingerprintSourceAttribute.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\FingerprintSourceAttribute.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\FingerprintType.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\FingerprintType.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\FingerprintValueNormalization.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\FingerprintValueNormalization.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\IFingerprint.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\IFingerprint.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Common\Shared\Eligibility\RiskManagement\Fingerprinting\IFingerprintable.cs">
            <Link>Eligibility\RiskManagement\Fingerprinting\IFingerprintable.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Eligibility\Contracts\IEligibilitySessionInterfaces.cs">
            <Link>DTO\IEligibilitySessionInterfaces.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Eligibility\DTO\OutcomeRequest.cs">
            <Link>DTO\OutcomeRequest.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Eligibility\DTO\OutcomeResponse.cs">
            <Link>DTO\OutcomeResponse.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Eligibility\DTO\TransmitAndEvaluate\EvaluateResponse.cs">
            <Link>DTO\TransmitAndEvaluate\EvaluateResponse.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Eligibility\DTO\TransmitAndEvaluate\OrderSource.cs">
            <Link>DTO\TransmitAndEvaluate\OrderSource.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Eligibility\DTO\TransmitAndEvaluate\TransactionType.cs">
            <Link>DTO\TransmitAndEvaluate\TransactionType.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Eligibility\DTO\TransmitAndEvaluate\TransmitRequest.cs">
            <Link>DTO\TransmitAndEvaluate\TransmitRequest.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Eligibility\DTO\TransmitAndEvaluate\TransmitResponse.cs">
            <Link>DTO\TransmitAndEvaluate\TransmitResponse.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Merchants\DTO\BankAccountInformationDTO.cs">
	    <Link>DTO\BankAccountInformationDTO.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Merchants\DTO\ContactDTO.cs">
	    <Link>DTO\ContactDTO.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Merchants\DTO\MerchantDTOS\MerchantCustomerSupportInformationDTO.cs">
		  <Link>DTO\MerchantCustomerSupportInformationDTO.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Merchants\DTO\PublicDTOS\MerchantPublicResponse.cs">
	    <Link>DTO\MerchantPublicResponse.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Merchants\DTO\SiteDTO.cs">
	    <Link>DTO\SiteDTO.cs</Link>
      </Compile>
        <Compile Include="..\FlexCharge.Merchants\Enums\StatusCodes.cs">
            <Link>DTO\StatusCodes.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\AddressDTO.cs">
            <Link>DTO\AddressDTO.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\ApplyOrderActivityResponse.cs">
            <Link>DTO\ApplyOrderActivityResponse.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Batches\BatchDTO.cs">
            <Link>DTO\BatchDTO.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\ContactDTO.cs">
            <Link>DTO\ContactDTO.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Dashboard\DashboardAggregatedDataDTOS.cs">
            <Link>DTO\Dashboard\DashboardAggregatedDataDTOS.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Dashboard\DashboardDataDTOS.cs">
            <Link>DTO\Dashboard\DashboardDataDTOS.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Dashboard\OrdersReport.cs">
	    <Link>DTO\Dashboard\OrdersReport.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\EmailDTO.cs">
	    <Link>DTO\EmailDTO.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\InternalReportsDTOResponse.cs">
	    <Link>DTO\InternalReportsDTOResponse.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\OrderCreateResponse.cs">
	    <Link>DTO\OrderCreateResponse.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\OrderDTO.cs">
          <Link>DTO\OrderDTO.cs</Link>
      </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\OrderUpdateResponse.cs">
            <Link>DTO\OrderUpdateResponse.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Payouts\PayoutDetailsDTO.cs">
            <Link>DTO\Payouts\PayoutDetailsDTO.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Payouts\PayoutStatus.cs">
            <Link>DTO\PayoutStatus.cs</Link>
        </Compile>
		<Compile Include="..\FlexCharge.Orders\DTO\Payouts\SettlementQueryDTO.cs">
			<Link>DTO/Payouts/SettlementQueryDTO.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Payouts\ValidatorsAttributes.cs">
            <Link>DTO\Payouts\ValidatorsAttributes.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Refunds\IRequestWithOptionalMid.cs">
            <Link>DTO\Refunds\IRequestWithOptionalMid.cs</Link>
        </Compile>
        <Compile Include="..\FlexCharge.Orders\DTO\Refunds\RefundRequest.cs">
            <Link>DTO\Refunds\RefundRequest.cs</Link>
        </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\Refunds\RefundResult.cs">
	    <Link>DTO\Refunds\RefundResult.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\Refunds\RefundStatus.cs">
	    <Link>DTO\Refunds\RefundStatus.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\Refunds\VoidRequest.cs">
	    <Link>DTO\Refunds\VoidRequest.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\Refunds\VoidResult.cs">
	    <Link>DTO\Refunds\VoidResult.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\Refunds\VoidStatus.cs">
	    <Link>DTO\Refunds\VoidStatus.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\DTO\TransactionDTO.cs">
	    <Link>DTO\TransactionDTO.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Orders\Entities\Enums.cs">
	    <Link>DTO\Enums.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Payments\DTO\PaymentInstrumentsDTOS.cs">
	    <Link>DTO\PaymentInstrumentsDTOS.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Payments\Entities\Enums.cs">
	    <Link>DTO\Enums.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Payments\Services\PaymentServices\Models\PaymentEnums.cs">
	    <Link>DTO\PaymentEnums.cs</Link>
	  </Compile>
	  <Compile Include="..\FlexCharge.Payments\Services\SDKs\SpreedlyService\Models\CreatePaymentInstrumentRequest.cs">
	    <Link>DTO\CreatePaymentInstrumentRequest.cs</Link>
	  </Compile>
	</ItemGroup>

<!--	<Choose>-->
<!--		<When Condition=" '$(Configuration)'=='Staging' ">-->
<!--			<ItemGroup>-->
<!--				<Content Remove="appsettings.Development.json" />-->

<!--				&lt;!&ndash; Other files you want to update in the scope of Debug &ndash;&gt;-->
<!--				<None Update="other_files">-->
<!--					<CopyToOutputDirectory>Never</CopyToOutputDirectory>-->
<!--				</None>-->
<!--			</ItemGroup>-->
<!--		</When>-->
<!--		<When Condition=" '$(Configuration)'=='Development' ">-->
<!--			<ItemGroup>-->
<!--				<Content Remove="appsettings.Staging.json" />-->

<!--				&lt;!&ndash; Other files you want to update in the scope of Debug &ndash;&gt;-->
<!--				<None Update="other_files">-->
<!--					<CopyToOutputDirectory>Never</CopyToOutputDirectory>-->
<!--				</None>-->
<!--			</ItemGroup>-->
<!--		</When>-->
<!--	</Choose>-->

</Project>
